<?php

use common\models\College;
use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\LiveUpdate;
use common\services\CollegeService;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;

$previousYear = \Yii::$app->params['previousYear'];
$defaultSeoInfo = ContentHelper::getCollegeDefaultSeoInfo(!empty($college->display_name) ? $college->display_name : $college->name, 'hostel');
$defaultTitle = $college->display_name . ' Placements ' . $previousYear . ': Highest & Average Salary Package, Top Companies';
$this->title = !empty($content->meta_title) ? CollegeHelper::parseContent($content->meta_title) : $defaultSeoInfo['title'];
$this->context->description = !empty($content->meta_description) ? CollegeHelper::parseContent($content->meta_description) : $defaultSeoInfo['description'];
$this->context->ogImage = !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner();
$isMobile = \Yii::$app->devicedetect->isMobile();

$authorImage = !empty($content->author->profile->image) ? (Yii::getAlias('@profileDPFrontend') . '/' . $content->author->profile->image) : '/yas/images/usericon.png';

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$url = (new CollegeService)->checkFilterPageStatus([], !empty($city) ? $city->slug : '', '', '') == College::SPONSORED_NO ? '/all-colleges/' . (!empty($city) ? $city->slug : '') : '';
if (!empty($url) && !empty($city)) {
    $this->params['breadcrumbs'][] = ['label' => 'Colleges in ' . $city->name, 'url' => [$url], 'title' => 'Colleges in ' . $college->city->name];
}
$this->params['breadcrumbs'][] = ['label' => !empty($college->display_name) ? $college->display_name : $college->name, 'url' => [$college->slug], 'title' => !empty($college->display_name) ? $college->display_name : $college->name];
$this->params['breadcrumbs'][] = 'Hostel';

$this->registerLinkTag(['href' => !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
$this->registerLinkTag(['href' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'college-new.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');

// robots meta tag for no-index
if ($college->is_index == College::IS_NO_INDEX_NO) {
    $this->params['robots'] = 'noindex';
}

$this->params['entity'] = College::ENTITY_COLLEGE;
$this->params['course_count'] = !empty($courseCount) ? $courseCount : '';
$this->params['entity_id'] = $college->id ?? 0;
$this->params['interested_location'] = $city->id ?? null;
$this->params['entity_name'] = $college->name ?? '';
$this->params['entitySlug'] = $college->slug ?? '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = $college->display_name ?? '';
$this->params['pageName'] = 'hostel';

// schema
$this->params['schema'] = CollegeHelper::collegeSchema($college, $revCategoryRating, $reviewCount);
?>

<div class="subPage">
    <!-- header -->
    <?= $this->render('partials/_header-new', [
        'menus' => $menus ?? [],
        'college' => $college,
        'content' => $content,
        'pageName' => 'hostel',
        'rating' => $revCategoryRating ? CollegeHelper::getTotalRating($revCategoryRating) : '',
        'reviewCount' => $reviewCount ?? '',
        'type' => $type,
        'approval' => $approval,
        'brochure' => $brochure,
        'heading' => !empty($content->h1) ? CollegeHelper::parseContent($content->h1) : (empty($defaultSeoInfo['h1']) ? $defaultSeoInfo['title'] : $defaultSeoInfo['h1']),
        'categoryRating' => $revCategoryRating,
        'city' => $city,
        'state' => $state,
        'author' => $authorDetail,
        'profile' => $profile,
        'dynamicCta' => $dynamicCta,
        'sponsorClientUrl' => !empty($sponsorClientUrl->redirection_link)  ? $sponsorClientUrl->redirection_link : '',
    ]) ?>

    <?php /*if (!empty($sponsorClientUrl->redirection_link)):?>
        <?= $this->render('partials/_notification', [
        'sponsorClientUrl' => $sponsorClientUrl,
        'college' => $college,
    ]) ?>

    <?php endif;*/ ?>

    <?= $this->render('partials/_menu-card', [
        'menus' => $menus,
        'college' => $college,
        'pageName' => 'hostel',
        'dropdown' => $dropdowns
    ]) ?>

    <div class="row">
        <div class="col-md-8">
            <?php if (!empty($collegeNotificationUpdate)): ?>
                <div class="pageData <?= count($collegeNotificationUpdate) > 5  ? 'pageInfo' : '' ?>">
                    <div class="infoPage">
                        <?= $this->render('../partials/_collegeNotificationUpdate', [
                            'collegeNotificationUpdate' => $collegeNotificationUpdate,
                            'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Hostel',
                            'isShowTitle' => true
                        ]) ?>
                    </div>
                </div>
            <?php endif; ?>
            <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU() && !$isMobile): ?>
                <aside>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                            <?php echo Ad::unit('GMU_COLLEGE_PLACEMENTS_WEB_728x90_ATF', '[728,90]') ?>
                        </div>
                    </div>
                </aside>
            <?php endif;

            if (!empty($content->content)): ?>
                <?= $this->render('partials/_main-content-card', [
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Hostel',
                    'content' => CollegeHelper::parseContent(ContentHelper::htmlDecode($content->content)),
                    'contentAuthor' => $content,
                    'author' => $authorDetail,
                    'profile' => $profile,
                    'removeShowMore' => true,
                    'recentActivity' => $recentActivity,
                    'examContentTemplate' => $examContentTemplate,
                    'collegeNotificationUpdate' => $collegeNotificationUpdate,

                ]) ?>
            <?php endif;
            if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU() && !$isMobile):
                ?>
                <aside>
                    <div class="horizontalRectangle desktopOnly">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__728x90 __336x280') ?>
                        </div>
                    </div>
                </aside>
            <?php endif;
            if ($liveApplicationForm && $isMobile && $college->is_sponsored == College::SPONSORED_NO && Url::toDomain() !=  Url::toBridgeU()): ?>
                <div id="liveApplicationForm"></div>
            <?php endif; ?>
            <?php if (!empty($faqs)): ?>
                <?= $this->render('partials/_faq-card', [
                    'faqs' => $faqs,
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Hostel FAQs',
                    'displayName' => (!empty($college->display_name) ? $college->display_name : $college->name),
                    'pageName' => 'Hostel'
                ]) ?>

                <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
                    <aside>
                        <div class="horizontalRectangle">
                            <div class="appendAdDiv  xs-h100" style="background:#EAEAEA;">
                                <?php /* if ($isMobile): ?>
                                    <?php echo Ad::unit('GMU_COLLEGE_PLACEMENTS_WAP_300x100_ATF', '[300,100]') ?>
                                <?php else: */ ?>
                                <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_336x280', '__240x400 __336x280') ?>
                                <?php //endif;
                                ?>
                            </div>
                        </div>
                    </aside>
                <?php endif;
            endif;
            ?>
            <div class="removeFixedQuickLink">
                <!-- Do not Delete this -->
            </div>
            <!-- reviews widget -->
            <?php if (!empty($reviews)): ?>
                <?= $this->render('partials/_review-card', [
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Reviews',
                    'reviews' => $reviews,
                    'distrbutionRating' => $revDistributionRating,
                    'categoryRating' => $revCategoryRating,
                    'collegeId' => $college->id,
                    'viewAllURL' => !empty($menus) && !is_numeric($menus['reviews']) ? Url::toCollege($college->slug, 'reviews') : '',
                    'reviewCount' => $reviewCount ?? '',
                    'category' => 5
                ]) ?>
            <?php endif; ?>

        </div>
        <div class="col-md-4 noSticky">
            <aside>

                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="getSupport">
                        <!-- <div class="row">
                            <img class="lazyload" loading="lazy" width="80" height="80" data-src="/yas/images/bulbIcon.svg" src="/yas/images/bulbIcon.svg" alt="">
                            <p>Are You Interested In This College?</p>
                        </div> -->
                        <div class="lead-cta lead-cta-cls-button" data-lead_cta="0" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>"></div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($college->parent_id)): ?>
                    <?= $this->render('partials/_main-campus', [
                        'college' => $parentCollege,
                    ]) ?>
                <?php endif; ?>

                <?php if ($liveApplicationForm && !$isMobile && $college->is_sponsored == College::SPONSORED_NO && Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div id="liveApplicationForm"></div>
                <?php endif; ?>

                <?php /* if (!empty($featuredNews) || !empty($recentNews)): ?>
                    <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                        'featured' => $featuredNews,
                        'recents' => $recentNews,
                        'isAmp'  => 0,
                        'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                        'smallIcone' => 1
                    ]); ?>
                <?php endif; ?>

                <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                    <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                        'trendings' => $trendingArticles,
                        'recentArticles' => $recentArticles,
                    ]); ?>
                <?php endif; */ ?>
            </aside>
            <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
                <?= $this->render('partials/_sidebar-ads.php', [
                    'class' => 'desktopOnly',
                    'ads' => [
                        ['slot' => 'GMU_COLLEGE_PLACEMENTS_WEB_300x250_MTF_1', 'size' => '[300, 250]', 'isMobile' => false],
                        ['slot' => 'GMU_COLLEGE_PLACEMENTS_WEB_300x250_MTF_2', 'size' => '[300, 250]', 'isMobile' => false]
                    ]
                ]) ?>
            <?php endif; ?>
        </div>
    </div>

    <?php if (!empty($forums)): ?>
        <?= $this->render('partials/_forum-card', [
            'forums' => $forums,
            'viewAllUrl' => $college->slug
        ]) ?>
    <?php endif; ?>

    <!-- other colleges under university -->
    <?php if (!empty($affiliatedCollege) && !empty($parentCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Other Colleges under ' . (!empty($parentCollege['display_name']) ? $parentCollege['display_name'] : $parentCollege['name']),
            'colleges' => $affiliatedCollege,
            'city' => $city,
            'state' => $state,
        ]) ?>
    <?php endif; ?>

    <?php if (!empty($collegeByDiscipline) && count($collegeByDiscipline) > 2): ?>
        <?= $this->render('partials/_similar-college-card', [
            'collegeByDiscipline' => $collegeByDiscipline,
            'college' => $college
        ]) ?>
    <?php endif; ?>

    <!-- Nearby colleges -->
    <?php if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Explore Nearby Colleges',
            'colleges' => $nearByCollege,
            'viewAllUrl' => !empty($city) ? "{$city->slug}" : '',
            'city' => $city,
            'state' => $state,
        ]) ?>
    <?php endif; ?>


    <!-- Popular Colleges -->
    <?php if (!empty($popularCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Popular Colleges',
            'colleges' => $popularCollege,
            'city' => $city,
            'state' => $state,
        ])
        ?>
    <?php endif; ?>

    <!-- related article -->
    <?php if (!empty($article)): ?>
        <?= $this->render('../partials/_productArticleCard', [
            'relatedArticles' => $article,
        ]); ?>
    <?php endif; ?>

    <!-- related News -->
    <?php if (!empty($news)): ?>
        <?= $this->render('../partials/_productNewsCard', [
            'news' => $news,
        ]); ?>
    <?php endif; ?>

    <!-- advertisement -->
    <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
        <aside>
            <div class="horizontalRectangle">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php if ($isMobile): ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250') ?>
                    <?php else: ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90') ?>
                    <?php endif; ?>
                </div>
            </div>
        </aside>
    <?php endif; ?>
</div>
<?php /*if (!empty($sponsorClientUrl->redirection_link)):?>
    <?= $this->render('partials/_apply-now', [
        'sponsorClientUrl' => $sponsorClientUrl,
        'college' => $college
    ]) ?>
<?php  endif;*/ ?>