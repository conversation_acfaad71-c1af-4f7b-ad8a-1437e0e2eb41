<?php

use common\models\College;
use common\helpers\ContentHelper;
use common\helpers\CollegeHelper;
use common\helpers\ReviewHelper;
use common\models\LiveUpdate;
use common\services\CollegeService;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;

$defaultSeoInfo = ContentHelper::getCollegeDefaultSeoInfo(!empty($college->display_name) ? $college->display_name : $college->name, 'images-videos');
$this->title = !empty($content->meta_title) ? CollegeHelper::parseContent($content->meta_title) : $defaultSeoInfo['title'];
$this->context->description = !empty($content->meta_description) ? CollegeHelper::parseContent($content->meta_description) : $defaultSeoInfo['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->context->ogImage = !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner();

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$url = (new CollegeService)->checkFilterPageStatus([], !empty($city) ? $city->slug : '', '', '') == College::SPONSORED_NO ? '/all-colleges/' . (!empty($city) ? $city->slug : '') : '';
if (!empty($url) && !empty($city)) {
    $this->params['breadcrumbs'][] = ['label' => 'Colleges in ' . $city->name, 'url' => [$url], 'title' => 'Colleges in ' . $city->name];
}
$this->params['breadcrumbs'][] = ['label' => !empty($college->display_name) ? $college->display_name : $college->name, 'url' => [$college->slug], 'title' => !empty($college->display_name) ? $college->display_name : $college->name];
$this->params['breadcrumbs'][] = 'Gallery';

$this->registerLinkTag(['href' => !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
$this->registerLinkTag(['href' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
// page specific assets
$this->registerCssFile('https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css');
// $this->registerCssFile('/yas/css/version2/side_bar.css', ['depends' => [AppAsset::class]]);

$this->registerCssFile(Yii::$app->params['cssPath'] . 'college-new.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');

// robots meta tag for no-index
if ($college->is_index == College::IS_NO_INDEX_NO) {
    $this->params['robots'] = 'noindex';
}

//gmu params
$this->params['entity'] = College::ENTITY_COLLEGE;
$this->params['course_count'] = !empty($courseCount) ? $courseCount : '';
$this->params['entity_id'] = $college->id ?? 0;
$this->params['interested_location'] = $city->id ?? null;
$this->params['entity_name'] = $college->name ?? '';
$this->params['entitySlug'] = $college->slug ?? '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = $college->display_name ?? '';
$this->params['pageName'] = 'images-videos';


// schema
$this->params['schema'] = CollegeHelper::collegeSchema($college, $revCategoryRating, $reviewCount);
?>

<div class="gallerypage">

    <?= $this->render('partials/_header-new', [
        'menus' => $menus ?? [],
        'college' => $college,
        'content' => $content,
        'pageName' => 'images-videos',
        'rating' => $revCategoryRating ? CollegeHelper::getTotalRating($revCategoryRating) : '',
        'reviewCount' => $reviewCount ?? '',
        'type' => $type,
        'approval' => $approval,
        'brochure' => $brochure,
        'heading' => !empty($content->h1) ? CollegeHelper::parseContent($content->h1) : (empty($defaultSeoInfo['h1']) ? $defaultSeoInfo['title'] : $defaultSeoInfo['h1']),
        'categoryRating' => $revCategoryRating,
        'city' => $city,
        'state' => $state,
        'dynamicCta' => $dynamicCta,
        'sponsorClientUrl' => !empty($sponsorClientUrl->redirection_link)  ? $sponsorClientUrl->redirection_link : '',
        'author' => [],
    ]) ?>

    <?php /*if (!empty($sponsorClientUrl->redirection_link)):?>
        <?= $this->render('partials/_notification', [
        'sponsorClientUrl' => $sponsorClientUrl,
        'college' => $college,
    ]) ?>
    <?php endif;*/ ?>

    <?= $this->render('partials/_menu-card', [
        'menus' => $menus,
        'college' => $college,
        'pageName' => 'images-videos',
        'dropdown' => $dropdowns
    ]) ?>
    <div class="row">
        <div class="col-md-8">
            <?php if ($college->is_google_ads == College::ADS_ACTIVE && !$isMobile): ?>
                <aside>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                            <?php echo Ad::unit('GMU_COLLEGE_GALLERY_WEB_728x90_ATF', '[728,90]') ?>
                        </div>
                    </div>
                </aside>
            <?php endif; ?>

            <?php if (!empty($collegeImages['images']) || (isset($studentReviewImages) && !empty($studentReviewImages[0]))): ?>
                <div class="pageData pb-0">
                    <?php if (!empty($collegeImages['images'])): ?>
                        <?= $this->render('partials/_gallery-card', [
                            'images' => $collegeImages['images'],
                            'college' => $college
                        ]) ?>
                    <?php endif; ?>

                    <?php if (isset($studentReviewImages) && !empty($studentReviewImages[0]) && !empty($studentReviewImages)): ?>
                        <h2>Student uploaded Images</h2>
                        <div class="galleryImageList row">
                            <?php $i = 1;
                            foreach ($studentReviewImages as $images): ?>
                                <?php if (empty($images)): ?>
                                    <?php continue; ?>
                                <?php endif; ?>
                                <?php foreach ($images as $image): ?>
                                    <?php if (empty($image) || empty($image['file'])): ?>
                                        <?php continue; ?>
                                    <?php endif; ?>
                                    <div class="galleryImage">
                                        <a href="<?= ReviewHelper::getReviewImages($image['file']) ?>" data-fancybox="gallery" data-caption="Caption Images <?= $i ?>">
                                            <img class="lazyload" loading="lazy" width="178" height="140" data-src="<?= ReviewHelper::getReviewImages($image['file']) ?>" src="<?= ReviewHelper::getReviewImages($image['file']) ?>" alt="">
                                        </a>
                                    </div>
                                    <?php $i++;
                                endforeach;
                            endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            <?php /* if ($college->is_google_ads == College::ADS_ACTIVE && $isMobile): ?>
                <aside>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                            <?php echo Ad::unit('GMU_COLLEGE_GALLERY_WAP_300x100_ATF', '[300,100]') ?>
                        </div>
                    </div>
                </aside>
            <?php endif; */ ?>

            <?php if ($liveApplicationForm && $isMobile && $college->is_sponsored == College::SPONSORED_NO): ?>
                <div id="liveApplicationForm"></div>
            <?php endif; ?>

            <?php
            if (!empty($collegeImages['videos'])):
                ?>
                <div class="pageData pb-0">
                    <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> Videos</h2>
                    <div class="collegeVideos row">
                        <?php foreach ($collegeImages['videos'] as $video): ?>
                            <div class="collegeVideoCard">
                                <img class="lazyload" loading="lazy" data-src="https://i.ytimg.com/vi/<?= $video ?>/0.jpg" src="https://i.ytimg.com/vi/<?= $video ?>/0.jpg" alt="">
                                <a data-fancybox="" data-width="1040" data-height="660" href="https://www.youtube.com/watch?v=<?= $video ?>" tabindex="-1" rel="nofollow">
                                    <i class="playBtnIcon"></i>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif ?>
            <div class="removeFixedQuickLink">
                <!-- Do not Delete this -->
            </div>
        </div>
        <div class="col-md-4">
            <aside>
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="getSupport">
                        <!-- <div class="row">
                            <img class="lazyload" loading="lazy" width="80" height="80" data-src="/yas/images/bulbIcon.svg" src="/yas/images/bulbIcon.svg" alt="">
                            <p>Are You Interested In This College?</p>
                        </div> -->
                        <div class="lead-cta lead-cta-cls-button" data-lead_cta="0" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>"></div>
                    </div>
                <?php endif; ?>
                <?php if (!empty($college->parent_id)): ?>
                    <?= $this->render('partials/_main-campus', [
                        'college' => $parentCollege,
                    ]) ?>
                <?php endif; ?>

                <?php if ($liveApplicationForm && !$isMobile && $college->is_sponsored == College::SPONSORED_NO): ?>
                    <div id="liveApplicationForm"></div>
                <?php endif; ?>

                <?php /*if (!empty($featuredNews) || !empty($recentNews)): ?>
                    <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                        'featured' => $featuredNews,
                        'recents' => $recentNews,
                        'isAmp'  => 0,
                        'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                        'smallIcone' => 1
                    ]); ?>
                <?php endif; ?>

                <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                    <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                        'trendings' => $trendingArticles,
                        'recentArticles' => $recentArticles,
                    ]); ?>
                <?php endif; */ ?>

            </aside>
            <?php if ($college->is_google_ads == College::ADS_ACTIVE): ?>
                <?= $this->render('partials/_sidebar-ads.php', [
                    'class' => 'desktopOnly',
                    'ads' => [
                        ['slot' => 'GMU_COLLEGE_GALLERY_WEB_300x250_MTF_1', 'size' => '[300, 250]', 'isMobile' => false],
                        ['slot' => 'GMU_COLLEGE_GALLERY_WEB_300x250_MTF_2', 'size' => '[300, 250]', 'isMobile' => false]
                    ]
                ]) ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- other colleges under university -->
    <?php if (!empty($affiliatedCollege) && !empty($parentCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Other Colleges under ' . (!empty($parentCollege['display_name']) ? $parentCollege['display_name'] : $parentCollege['name']),
            'colleges' => $affiliatedCollege,
            'city' => $city,
            'state' => $state
        ]) ?>
    <?php endif; ?>

    <?php if (!empty($collegeByDiscipline) && !empty($collegeByDiscipline['colleges']) && count($collegeByDiscipline['colleges']) > 2): ?>
        <?= $this->render('partials/_similar-college-card', [
            'collegeByDiscipline' => $collegeByDiscipline['colleges'],
            'sponsorStatus' => $collegeByDiscipline['sponsorStatus'],
            'college' => $college
        ]) ?>
    <?php endif; ?>

    <?php if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Explore Nearby Colleges',
            'colleges' => $nearByCollege,
            'viewAllUrl' => !empty($city) ? "{$city->slug}" : '',
            'city' => $city,
            'state' => $state
        ]) ?>
    <?php endif; ?>

    <!-- Popular Colleges -->
    <?php if (!empty($popularCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Popular Colleges',
            'colleges' => $popularCollege,
            'city' => $city,
            'state' => $state
        ])
        ?>
    <?php endif; ?>

    <!-- related article -->
    <?php if (!empty($article)): ?>
        <?= $this->render('../partials/_productArticleCard', [
            'relatedArticles' => $article,
        ]); ?>
    <?php endif; ?>

    <!-- related News -->
    <?php if (!empty($news)): ?>
        <?= $this->render('../partials/_productNewsCard', [
            'news' => $news,
        ]); ?>
    <?php endif;

    if ($college->is_google_ads == College::ADS_ACTIVE): ?>
        <aside>
            <div class="horizontalRectangle">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php if ($isMobile): ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250') ?>
                    <?php else: ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90') ?>
                    <?php endif; ?>
                </div>
            </div>
        </aside>
    <?php endif; ?>
</div>
<?php /*if (!empty($sponsorClientUrl->redirection_link)):?>
    <?= $this->render('partials/_apply-now', [
        'sponsorClientUrl' => $sponsorClientUrl,
        'college' => $college
    ]) ?>
<?php  endif;*/ ?>

<?php /*  echo $this->registerJsFile(
    Yii::$app->params['jsPath'].'/yas/js/version2/ajax/libs/js/jquery.fancybox.min.js',
    ['depends' => [\yii\web\JqueryAsset::class]]
);*/
?>