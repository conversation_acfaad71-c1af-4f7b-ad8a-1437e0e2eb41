<?php

use common\models\College;
use common\models\CollegeCourse;
use common\helpers\CollegeHelper;
use common\helpers\CollegeRanksDataHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\Lead;
use common\models\LiveUpdate;
use common\services\UserService;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use yii\helpers\Inflector;

$pName = $program->program;
$ptitle = $program->program;

$examData = [];
if (!empty($collegeExams)) {
    $examData = CollegeHelper::formateDate($collegeExams);
}

$defaultSeoInfo = ContentHelper::getCollegeDefaultSeoInfo(
    !empty($college->display_name) ? $college->display_name : $college->name,
    'program',
    $menus,
    [
        'salary' => !empty($mysqlProgram) ? $mysqlProgram->salary : '',
        'fees' => !empty($programFeeDetails['programFees']) ? $programFeeDetails['totalFees'] : null,
        'dates' => !empty($examData) ? $examData : null,
        'eligibility' => (!empty($programData->qualification) && $programData->qualification != '""') || !empty($courseEligibility) ? 'Eligibility' : null,
    ],
    $pName
);

$this->title = !empty($programData->meta_title) ? CollegeHelper::parseContent($programData->meta_title) : $defaultSeoInfo['title'];
$this->context->description = !empty($programData->meta_description) ? CollegeHelper::parseContent($programData->meta_description) : $defaultSeoInfo['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->context->ogImage = !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner();

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
if (!empty($college->city->name)) {
    $this->params['breadcrumbs'][] = ['label' => 'Colleges in ' . $college->city->name, 'url' => ['/all-colleges/' . $college->city->slug], 'title' => 'Colleges in ' . $college->city->name];
}
$this->params['breadcrumbs'][] = ['label' => !empty($college->display_name) ? $college->display_name : $college->name, 'url' => ['/college/' . $college->slug], 'title' => !empty($college->display_name) ? $college->display_name : $college->name];
$this->params['breadcrumbs'][] = ['label' => 'Courses and Fees', 'url' => ['/college/' . $college->slug . '-courses-fees'], 'title' => 'Courses and Fees'];
$this->params['breadcrumbs'][] = $pName;

$this->registerLinkTag(['href' => !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
$this->registerLinkTag(['href' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'college-new.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');

// robots meta tag for no-index
if ($college->is_index == College::IS_NO_INDEX_NO) {
    $this->params['robots'] = 'noindex';
}

//gmu params
$this->params['entity'] = College::ENTITY_COLLEGE;
$this->params['course_count'] = !empty($courseCount) ? $courseCount : '';
$this->params['entity_id'] = $college->id ?? 0;
$this->params['interested_location'] = $college->city->id ?? null;
$this->params['entity_name'] = $college->name ?? '';
$this->params['entitySlug'] = $college->slug ?? '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = $college->display_name ?? '';
$this->params['pageName'] = 'program';
$this->params['course_id'] = $course->id;
$this->params['program_id'] = $program->program_id;
// schema
$this->params['schema'] = CollegeHelper::collegeSchema($college, $revCategoryRating, $reviewCount);

if (!empty($program->exams)) {
    $examsArr = explode(',', $program->exams);
    $examStr = implode(', ', $examsArr);
}

if ($isparam == 1) {
    $this->registerMetaTag(['name' => 'robots', 'content' => 'noindex, nofollow']);
}

//article (product mapping) cta location
$cta_location1 = isset($articleCtaLocation) ? UserService::parseDynamicCta($articleCtaLocation, '', 'left') : '';
$cta_location2 = isset($articleCtaLocation) ? UserService::parseDynamicCta($articleCtaLocation, '', 'right') : '';
$defaultEmptyCondition1 = empty($dynamicCta) && empty($dynamicCta['cta_position_2']) || empty(array_filter($dynamicCta['cta_position_2']));
$defaultEmptyCondition2 = empty($dynamicCta) && empty($dynamicCta['cta_position_3']) || empty(array_filter($dynamicCta['cta_position_3']));
?>

<div class="programPage">

    <?= $this->render('partials/_header-new', [
        'menus' => $menus ?? [],
        'college' => $college,
        'content' => $programData,
        'pageName' => 'program',
        'page_slug' => $program->program_slug,
        'accredited' => $accredited,
        'recognised' => $recognised,
        'rating' => $revCategoryRating ? CollegeHelper::getTotalRating($revCategoryRating) : '',
        'reviewCount' => $reviewCount ?? '',
        'type' => $type,
        'approval' => $approval,
        'brochure' => $brochure,
        'heading' => empty($programData['h1']) ? $defaultSeoInfo['h1'] : $programData['h1'],
        'categoryRating' => $revCategoryRating,
        'city' => $city,
        'state' => $state,
        'dynamicCta' => $dynamicCta ?? [],
        'programSlug' => $program->program_slug,
        'sponsorClientUrl' => !empty($sponsorClientUrl->redirection_link)  ? $sponsorClientUrl->redirection_link : '',
        'author' => !empty($author) ? $author : [],
    ]) ?>
    <?= $this->render('partials/_menu-card', [
        'menus' => $menus,
        'college' => $college,
        'pageName' => 'program',
        'dropdown' => $dropdowns,
    ]) ?>

    <div class="row">
        <div class="col-md-8">
            <?php if (!empty($programFeeDetails['programFees'])): ?>
                <div class="pageData programInfoTable centerAlignLastCell">
                    <h2><?= $college->display_name ?? $college->name ?> <?= $ptitle ?> Fees</h2>
                    <table>
                        <thead>
                            <tr>
                                <td>Year</td>
                                <?php if (!empty($programFeeDetails['programFees']['duration_type'])): ?>
                                    <?php foreach ($programFeeDetails['programFees']['duration_type'] as $ke => $type): ?>
                                        <td style="text-align: center;"><?= CollegeHelper::$feeTypes[$type] ?? '' ?></td>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                                <td>Total</td>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $row = !empty($programFeeDetails['programFees']['duration_type']) ? count($programFeeDetails['programFees']['duration_type']) : 0;
                            if (!empty($programFeeDetails['programFees']['feesData'])):
                                foreach (($programFeeDetails['programFees']['feesData']) as $key => $value):
                                    ?>
                                    <tr>
                                        <td>
                                            <?= $key ? CollegeHelper::numberSufixWithAlphebet($key) . ' Year ' : '' ?>
                                        </td>
                                        <?php $valueCount = count($value);
                                        foreach ($value as $k => $v):
                                            $totalArr[] = $v['price'];
                                            ?>
                                            <td style="text-align: center;"><?= '₹ ' . number_format($v['price']) . '/-' ?></td>
                                        <?php endforeach;
                                        $leftColumn = $row - $valueCount;
                                        if ($leftColumn !== 0):
                                            for ($i = 0; $i < $leftColumn; $i++):
                                                ?>
                                                <td style="text-align: center;">-</td>
                                            <?php endfor;
                                        endif;
                                        if (!empty($totalArr)): ?>
                                            <td><?= '₹ ' . number_format(array_sum($totalArr)) . '/-' ?></td>
                                        <?php endif; ?>
                                    </tr>
                                    <?php
                                    unset($totalArr);
                                endforeach;
                            endif;
                            ?>
                            <?php if (!empty($programFeeDetails['programFees']['oneTimeFees'])):
                                foreach ($programFeeDetails['programFees']['oneTimeFees'] as $k => $v):
                                    ?>
                                    <tr>
                                        <td colspan="<?= $row + 1 ?>"><?= CollegeHelper::$feeTypes[$k] ?> (One time) </td>
                                        <td><?= '₹ ' . number_format($v['total']) . '/-' ?></td>
                                    </tr>
                                <?php endforeach;
                            endif; ?>
                            <?php if (!empty($programFeeDetails['totalFees'])): ?>
                                <tr>
                                    <td colspan="<?= $row + 1 ?>"><b>Total Fees</b></td>
                                    <td><?= '₹ ' . number_format($programFeeDetails['totalFees']) . '/-' ?></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                    <div class="centerAlignButton">
                        <div class="lead-cta" data-lead_cta="18" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>" data-program="<?= $program->program_slug ?>"></div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (!empty($getCiSubpage)): ?>
                <?= $this->render('partials/ci_pi_subpage_related_links', [
                    'getCiSubpage' => $getCiSubpage,
                    'college_slug' => $college->slug,
                    'college_name' => $college->display_name ?? $college->name,
                    'page_name' => $pName,
                    'page' => 'pi',
                    'page_slug' => $program->program_slug,
                    'page_id' =>  $mysqlProgram->id
                ]) ?>
            <?php endif; ?>

            <div class="pageData programInfoDiv programInfoDiv-cls">
                <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> <?= $ptitle ?> Course Highlights</h2>
                <table>
                    <tbody>
                        <tr>
                            <td><span class="spriteIcon newCapIcon"></span> Course Name</td>
                            <td><?= $program->course ?></td>
                        </tr>
                        <?php if (!empty(DataHelper::$collegeCourseDegreeList[$program->degree])): ?>
                            <tr>
                                <td><span class="spriteIcon newSocialIcon"></span> Course Level</td>
                                <td><?= DataHelper::$collegeCourseDegreeList[$program->degree] ?></td>
                            </tr>
                        <?php endif; ?>
                        <?php if (!empty(CollegeCourse::$type[$program->type])): ?>
                            <tr>
                                <td><span class="spriteIcon newCapIcon"></span> Course Mode</td>
                                <td><?= CollegeCourse::$type[$program->type] ?? '--' ?></td>
                            </tr>
                        <?php endif; ?>
                        <tr>
                            <td><span class="spriteIcon newClockIcon"></span>Course Duration</td>
                            <td><?= CollegeHelper::yearsFormat($program->duration) ?></td>
                        </tr>
                        <?php if (!empty($program->total_seat)): ?>
                            <tr>
                                <td><span class="spriteIcon newWriteIcon"></span> Seats</td>
                                <td><?= $program->total_seat ?? '--' ?></td>
                            </tr>
                        <?php endif ?>
                        <?php if (!empty($program->application_link)): ?>
                            <tr>
                                <td><span class="spriteIcon newLinkIcon"></span> Application Link</td>
                                <td><?= $program->application_link ?? 'N/A' ?></td>
                            </tr>
                        <?php endif; ?>
                        <tr>
                            <td><span class="spriteIcon newFlagIcon"></span> Accreditations</td>
                            <td><?= $accredited ?? 'N/A' ?></td>
                        </tr>
                        <?php /*if (!empty($type)): ?>
                            <tr>
                                <td><span class="spriteIcon newInstituteIcon"></span> College Type</td>
                                <td><?= ucfirst($type) ?></td>
                            </tr>
                        <?php endif;*/ ?>
                        <?php if (!empty($ranking['rank'])): ?>
                            <tr>
                                <td><span class="spriteIcon newRankIcon"></span> Ranking</td>
                                <td><?= $ranking['rank']; ?> (NIRF <?= $ranking['year']; ?>)</td>
                            </tr>
                        <?php endif; ?>
                        <?php /*if (!empty($featuresGroup['Highlights'])):
                            foreach ($featuresGroup['Highlights'] as $value) {
                                if ($value['featureSlug'] == 'year-of-establishment') {
                                    $establishmentYear = $value['value'];
                                    break;
                                }
                            }
                            ?>
                            <?php if (!empty($establishmentYear)): ?>
                                <tr>
                                    <td><span class="spriteIcon newEmblemIcon"></span> Year of Establishment</td>
                                    <td><?= $establishmentYear; ?></td>
                                </tr>
                            <?php endif;
                            if (!empty($ranking['rank'])): ?>
                                <tr>
                                    <td><span class="spriteIcon newRankIcon"></span> Ranking</td>
                                    <td><?= $ranking['rank']; ?> (NIRF <?= $ranking['year']; ?>)</td>
                                </tr>
                            <?php endif;
                        endif; */ ?>
                    </tbody>
                </table>
            </div>

            <?php if (!empty($studentDiversityData)): ?>
                <div class="pageData collegeRankings">
                    <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> Student Diversity</h2>
                    <table class="courseFeesTable rankTable">
                        <thead>
                            <tr>
                                <td>Category</td>
                                <?php if (!empty($studentDiversityData['category'])): ?>
                                    <?php foreach ($studentDiversityData['category'] as $key): ?>
                                        <td><?= $key ?></td>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $countCategory = count($studentDiversityData['category']);
                            unset($studentDiversityData['category']);
                            foreach ($studentDiversityData as $key => $val): ?>
                                <tr>
                                    <td><?= ($key == 'All') ? 'Total' : $key ?></td>
                                    <?php
                                    $rowcount = count($val);
                                    $leftColumn = $countCategory - $rowcount;
                                    foreach ($val as $k => $v): ?>
                                        <td><?= !empty($v) ?  $v :  '-' ?></td>
                                    <?php endforeach;
                                    if ($leftColumn !== 0):
                                        for ($i = 0; $i < $leftColumn; $i++):
                                            ?>
                                            <td style="text-align: center;">-</td>
                                        <?php endfor;
                                    endif;
                                    ?>
                                </tr>
                            <?php endforeach; ?>

                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
            <?php /*<div class="pageData programInfoDiv">
                <p><?= $program->program ?> is a <strong><?= CollegeHelper::yearsFormat($program->duration) ?> <?= !empty(DataHelper::$collegeCourseDegreeList[$program->degree]) ? DataHelper::$collegeCourseDegreeList[$program->degree] : '' ?></strong> Course offered by <?= $college->name ?>.</p>
            </div>*/ ?>
            <?php /* if (!empty($collegeExams)): ?>
                <div class="pageData">
                    <h2 class="row">
                        Exams Accepted for <?= $program->program ?> at <?= !empty($college->display_name) ? $college->display_name : $college->name ?>
                    </h2>
                    <div class="customSlider two-cardDisplay">

                        <?php if (count($collegeExams) > 2): ?>
                            <i class="spriteIcon scrollLeft over"></i>
                            <i class="spriteIcon scrollRight"></i>
                        <?php endif; ?>

                        <div class="customSliderCards">
                            <?php foreach ($collegeExams as $exam): ?>
                                <div class="sliderCardInfo">
                                    <div class="row">
                                        <img class="clgLogo lazyload" width="72" height="72" loading="lazy" data-src="<?= !empty($exam['image']) ? Url::toExamImage($exam['image']) : 'https://media.getmyuni.com/yas/images/defaultcardbanner.png' ?>" src="<?= !empty($exam['image']) ? Url::toExamImage($exam['image']) : 'https://media.getmyuni.com/yas/images/defaultcardbanner.png' ?>" alt="<?= $exam['display_name'] ?>">
                                        <div>
                                            <p><a href="<?= Url::toExamDetail($exam['slug']) ?>"><?= $exam['display_name'] ?></a></p>
                                            <?php if (!empty($exam['date'])): ?>
                                                <?php if (!empty($exam['date']['exam_start'])):
                                                    $examStart = explode('|', $exam['date']['exam_start']);
                                                    ?>
                                                    <p>Exam Date: <span><?= date('M d, Y', strtotime($examStart[0])) ?></span></p>
                                                <?php endif; ?>
                                                <?php if (!empty($exam['date']['result_date'])):
                                                    $restultDate = explode('|', $exam['date']['result_date']);
                                                    ?>
                                                    <p>Result Date: <span><?= date('M d, Y', strtotime($restultDate[0])) ?></span></p>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif;*/ ?>

            <?php /*if (!empty($programFeeDetails['feesData'])): ?>
                <div class="pageData programInfoTable centerAlignLastCell">
                    <h2><?= $college->display_name ?? $college->name ?> <?= $ptitle ?> Fees</h2>
                    <table>
                        <thead>
                            <tr>
                                <td>Fee Components</td>
                                <td>t</td>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (($programFeeDetails['feesData']) as $key => $value):
                                foreach ($value as $k => $v):
                                    ?>
                                    <tr>
                                        <td>
                                            <?= $v['year'] ? CollegeHelper::numberSufixWithAlphebet($v['year']) . ' Year ' : '' ?><?= CollegeHelper::$feeTypes[$key] ?>
                                            <?= $v['fees_content'] ?? '' ?>
                                        </td>
                                        <td><?= '₹ ' . number_format($v['price']) . '/-' ?></td>
                                    </tr>
                                    <?php
                                endforeach;
                            endforeach;
                            ?>
                            <?php if (!empty($programFeeDetails['feesData']['tuition_fees'])): ?>
                                <tr>
                                    <td><b>Total Fees</b></td>
                                    <td><?= '₹ ' . number_format($programFeeDetails['totalFees']) . '/-' ?></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                    <div class="centerAlignButton">
                        <div class="lead-cta" data-lead_cta="18" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>" data-program="<?= $program->program_slug ?>"></div>
                    </div>
                </div>
            <?php endif;*/ ?>

            <?php
            if (isset($programData->qualification) && !empty($programData->qualification)) {
                $programDetails = $details = json_decode($programData->qualification);
            }
            if ((!empty($programDetails->eligibility) || !empty($programDetails->exams)) || !empty($courseEligibility)): ?>
                <div class="pageData programInfoDiv programInfoTable">
                    <h2><?= $college->display_name ?? $college->name ?> <?= $ptitle ?> Eligibility</h2>
                    <p>Students who wish to pursue <?= $college->display_name ?? $college->name ?> <?= $program->program ?> must fulfill the minimum eligibilty criteria as mentioned below. <?= (!empty($program->exams)) ? 'Admission to the course is also based on the scores obtained in ' . isset($examStr) ? $examStr : '' : '' ?>.</p>
                    <?php
                    if (!empty($details->eligibility) || (!empty($details->exams)) || !empty($courseEligibility)): ?>
                        <table>
                            <thead>
                                <tr>
                                    <th>Educational Qualification</th>
                                    <th>Required Eligibility</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if (!empty($details->eligibility)):
                                    foreach ($details->eligibility as $value):
                                        ?>
                                        <tr>
                                            <td><?= $value->educational_qualification ?></td>
                                            <td> <?= $value->marks . ' ' . $value->scale ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                    <?php
                                elseif (!empty($courseEligibility)):
                                    foreach ($courseEligibility as $value):
                                        ?>
                                        <tr>
                                            <td><?= $value->eligibility_title ?></td>
                                            <td> <?= $value->eligibility_description ?></td>
                                        </tr>
                                    <?php endforeach;
                                endif;
                                if (!empty($details->exams)):
                                    foreach ($details->exams as $value):
                                        // if (!empty($value->marks)):
                                        if (!empty($value->mark)):
                                            ?>
                                            <tr>
                                                <td><a href="<?= Url::toExamDetail(Inflector::slug($value->exams, '-')) ?>"><?= $value->exams ?></a></td>
                                                <td> <?= $value->marks ?></td>
                                            </tr>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                    <div class="centerAlignButton">
                        <div class="lead-cta" data-lead_cta="17" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>" data-program="<?= $program->program_slug ?>"></div>
                    </div>
                </div>
            <?php endif; ?>


            <?php if (!empty($mysqlProgram->salary)): ?>
                <div class="pageData placementDiv">
                    <h2><?= $college->display_name ?? $college->name ?> <?= $ptitle ?> Placements</h2>
                    <?php if (!empty($mysqlProgram->salary)): ?>
                        <p>Median Salary For <strong><?= $program->program ?></strong> for past years is <strong>INR <?= CollegeHelper::feesFormat($mysqlProgram->salary) ?></strong>.</p>
                    <?php endif; ?>
                    <?php if (!empty($companyList)): ?>
                        <p>Students from this course were placed in following companies.</p>
                        <h4 class="listHeading">Placement Companies</h4>
                        <ul class="placementList">
                            <?php $i = 0;
                            foreach ($companyList as $list): ?>
                                <li class="<?= $i > 20 ?: 'hideCompanya' ?>"><?= $list['name'] ?></li>
                                <?php $i++;
                            endforeach; ?>
                        </ul>
                    <?php endif; ?>
                    <?php if (count($companyList) > 20): ?>
                        <a class="viewAllCompanies">View All Companies</a>
                    <?php endif; ?>
                    <div class="centerAlignButton">
                        <div class="lead-cta" data-lead_cta="19" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>" data-program="<?= $program->program_slug ?>"></div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (!empty($dates)): ?>
                <?= $this->render('partials/course_program_dates', [
                    'dates' => $dates,
                    'title' => $ptitle,
                    'college' => $college,
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($streamRanks)): ?>
                <?= $this->render('partials/_course_program_ranking', [
                    'streamRanks' => $streamRanks,
                    'title' => $ptitle,
                    'college' => $college,
                ]) ?>
            <?php endif; ?>

            <?php /*if (!empty($cutOff['dataProvider']->allModels)): ?>
                <section class="filterSection filtersAll cutoffFilterSection">
                    <?= $this->render('partials/_cut-off-filter', [
                        'model' => $cutOff['searchModel'],
                        'college' => $college,
                    ]) ?>
                </section>

                <section class="cutOffDetailSection">
                    <div class="courseCutOffTypeList">
                        <?php if (!empty($cutOff['dataProvider']->allModels)): ?>
                            <?= $this->render(
                                'partials/_cut-off-list',
                                [
                                    'models' => $cutOff['dataProvider']->allModels,
                                    'college' => $college,
                                    'state' => $state,
                                    // 'page' => 'cut-off',
                                    'isMobile' => $isMobile,
                                ]
                            ); ?>
                        <?php else: ?>
                            <div> NO Result Found</div>
                        <?php endif; ?>
                    </div>
                </section>
            <?php endif; */ ?>

            <?php if (!empty($examData)): ?>
                <?= $this->render('partials/_course_program_imp_dates_exams', [
                    'examData' => $examData,
                    'title' => $ptitle,
                    'college' => $college,
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($programData->content)): ?>
                <div class="pageData pageInfo">
                    <?= ContentHelper::htmlDecode(ContentHelper::removeStyleTag(stripslashes(
                        DataHelper::parseDomainUrlInContent($programData->content)
                    ))); ?>
                </div>
            <?php else:
                if (!empty($contentTemplate)): ?>
                    <div class="pageData pageInfo">
                            <?= ContentHelper::htmlDecode(ContentHelper::removeStyleTag(stripslashes(
                                DataHelper::parseDomainUrlInContent($contentTemplate)
                            ))); ?>
                    </div>
                <?php endif;
            endif; ?>
            <?php if (!empty($faqs) && $isMobile): ?>
                <?= $this->render('partials/_faq-card', [
                'faqs' => $faqs,
                'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' FAQs',
                'displayName' => (!empty($college->display_name) ? $college->display_name : $college->name),
                'pageName' => $pName ?? ''
        ]) ?>
            <?php endif; ?>
            <?php if (!empty($programList)): ?>
                <div class="pageData pageInfo courseAndFeeDiv ciPageTable" style="margin-bottom: 0px;">
                    <h2 class="courseAndFeeDivHeading">
                        Other <?= $course->short_name ?> Specializations offered by <College Short Name>
                            <?= !empty($college->display_name) ? $college->display_name : $college->name ?></h2>
                    <table>
                        <thead>
                            <tr>
                                <td>Course</td>
                                <td>Duration</td>
                                <td>Total Tuition Fees</td>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($programList as $list): ?>
                                <tr>
                                    <td>
                                        <a href="<?= Url::toCollegeCourseProgram($college->slug, $list['slug'], $list['page_index'] ?? '') ?>" title="<?= $college->display_name ?? $college->name ?> <?= $list['program'] ?? $course->short_name ?>">
                                            <?= $list['name'] ?>
                                        </a>
                                    </td>
                                    <td><?= CollegeHelper::yearsFormat($list['duration']) ?></td>
                                    <td><?= !empty($list['fee']) ? '₹' . ContentHelper::indMoneyFormat($list['fee']) :
                                            frontend\helpers\Html::leadButton(
                                                'Get Fees',
                                                [
                                                    'entity' => Lead::ENTITY_COLLEGE,
                                                    'entityId' => $college->id,
                                                    'stateId' => !empty($state->id) ? $state->id : '',
                                                    'interestedLocation' => !empty($college->city_id) ? $college->city_id : '',
                                                    'ctaLocation' => $isMobile ? UserService::parseDynamicCta('colleges_program_information_{slug}_wap_lead_rigth_cta4', '', $list['slug']) : UserService::parseDynamicCta('colleges_program_information_{slug}_web_lead_rigth_cta4', '', $list['slug']),
                                                    'ctaText' => 'Get Fees',
                                                    'subheadingtext' => 'Get latest updates and access to premium content',
                                                    'image' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo()
                                                ],
                                                ['class' => 'getLeadForm textBlue getFees']
                                            ) ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>

            <?php /*if (!empty($otherCourseList)):
                unset($otherCourseList[$course->name]);
                if (!empty($otherCourseList)):
                    ?>
                    <div class="pageData pageInfo courseAndFeeDiv ciPageTable" style="margin-bottom: 0px;">
                        <h2 class="courseAndFeeDivHeading">Other Courses Offered by <?= !empty($college->display_name) ? $college->display_name : $college->name ?></h2>
                        <table>
                            <thead>
                                <tr>
                                    <td>Course</td>
                                    <td>Duration</td>
                                    <td>Average Fees</td>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($otherCourseList as $list): ?>
                                    <tr>
                                        <td><a href="<?= Url::toCollegeCourse($list['slug'], $college->slug) ?>"><?= $list['short_name'] ?? $list['name'] ?></a></td>
                                        <td><?= !empty($list['course_duration']) ? CollegeHelper::yearsFormat($list['course_duration']) : '--' ?></td>
                                        <td><?= !empty($list['avgFees']) ? '₹' . ContentHelper::indMoneyFormat($list['avgFees']) :
                                                frontend\helpers\Html::leadButton(
                                                    'Get Fees',
                                                    [
                                                        'entity' => Lead::ENTITY_COLLEGE,
                                                        'entityId' => $college->id,
                                                        'stateId' => $state->id,
                                                        'interestedLocation' => $college->city_id,
                                                        'ctaLocation' => $isMobile ? UserService::parseDynamicCta('colleges_program_information_{slug}_wap_lead_rigth_cta5', '', $list['slug']) : UserService::parseDynamicCta('colleges_program_information_{slug}_web_lead_rigth_cta5', '', $list['slug']),
                                                        'ctaText' => 'Get Fees',
                                                        'leadformtitle' => 'Register now to get Detailed Fees',
                                                        'subheadingtext' => 'Get latest updates and access to premium content',
                                                        'image' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(),
                                                        'plpCourseSlug' => isset($list['slug']) && !empty($list['slug']) ? $list['slug'] : null,
                                                    ],
                                                    ['class' => 'getLeadForm textBlue getFees leadCourseCapture']
                                                ) ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif;
            endif;*/ ?>
        </div>
        <div class="col-md-4">
            <aside>
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="getSupport">
                        <!-- <div class="row">
                            <img class="lazyload" loading="lazy" width="80" height="80" data-src="/yas/images/bulbIcon.svg" src="/yas/images/bulbIcon.svg" alt="">
                            <p>Are You Interested In This College?</p>
                        </div> -->
                        <div class="lead-cta lead-cta-cls-button" data-lead_cta="0" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>" data-program="<?= $program->program_slug ?>"></div>
                    </div>
                <?php endif; ?>
                <?php if (!empty($otherSpecialization)): ?>
                    <div class="sideBarSection">
                        <p class="listCard">Get More information on</p>
                        <div class="sidebarLinks">
                            <?php foreach ($otherSpecialization as $other): ?>
                                <a href="<?= Url::toCourseDetail($other->slug) ?>" title="<?= $other->name ?>" class="listCard">
                                    <div class="sidebarTextLink">
                                        <p class="cardText"><?= $other->name; ?></p>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($specializationBasedState)): ?>
                    <div class="sideBarSection">
                        <p class="listCard">Top States for <?= $program->specialization_name ?></p>
                        <div class="sidebarLinks">
                            <?php foreach ($specializationBasedState as $list): ?>
                                <a href="/<?= $course->slug . '-' . $program->specialization_slug ?>-colleges/<?= $list['stateSlug'] ?>" title="<?= $program->specialization_name ?> colleges in <?= $list['stateName'] ?>" class="listCard">
                                    <div class="sidebarTextLink">
                                        <p class="cardText">Top <?= $program->specialization_name ?> Colleges In <?= $list['stateName'] ?></p>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($specializationBasedCity)): ?>
                    <div class="sideBarSection">
                        <p class="listCard">Top Cities for <?= $program->specialization_name ?></p>
                        <div class="sidebarLinks">
                            <?php foreach ($specializationBasedCity as $list): ?>
                                <a href="/<?= $course->slug . '-' . $program->specialization_slug ?>-colleges/<?= $list['citySlug'] ?>" title="<?= $program->specialization_name ?> colleges in <?= $list['cityName'] ?>" class="listCard">
                                    <div class="sidebarTextLink">
                                        <p class="cardText">Top <?= $program->specialization_name ?> Colleges In <?= $list['cityName'] ?></p>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php /*if (!empty($collegeList)): ?>
                    <div class="sideBarSection">
                        <p class="listCard">Top <?= $program->specialization_name ?> Colleges</p>
                        <div class="sidebarLinks">
                            <?php foreach ($collegeList as $list): ?>
                                <a href="<?= Url::toCollege($list['slug']) ?>" title="<?= $program->specialization_name ?> in <?= $list['display_name'] ?>" class="listCard">
                                    <div class="sidebarTextLink">
                                        <p class="cardText"><?= $program->specialization_name ?> in <?= $list['display_name'] ?></p>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif;*/ ?>

                <?php if (!empty($menus)): ?>
                    <div class="sideBarSection">
                        <p class="listCard">Know More About <?= !empty($college->display_name) ? $college->display_name : $college->name ?></p>
                        <div class="sidebarLinks">
                            <?php foreach ($menus as $k => $v):
                                if (in_array($k, ['info', 'reviews', 'qna', 'compare', 'hostel', 'syllabus', 'application-form', 'ranking', 'news', 'images-videos']) || is_numeric($v)) {
                                    continue;
                                }
                                ?>
                                <a href="<?= Url::toCollege($college->slug, $k) ?>" title="<?= !empty($college->display_name) ? $college->display_name : $college->name ?> <?= $v ?>" class="listCard">
                                    <div class="sidebarTextLink">
                                        <p class="cardText"><?= !empty($college->display_name) ? $college->display_name : $college->name ?> <?= isset(CollegeHelper::$collegePageCi[$k]) ? CollegeHelper::$collegePageCi[$k] : '' ?></p>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                <?php /*if (!empty($featuredNews) || !empty($recentNews)): ?>
                    <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                        'featured' => $featuredNews,
                        'recents' => $recentNews,
                        'isAmp'  => 0,
                        'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                        'smallIcone' => 1
                    ]); ?>
                <?php endif; ?>

                <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                    <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                        'trendings' => $trendingArticles,
                        'recentArticles' => $recentArticles,
                    ]); ?>
                <?php endif; */ ?>

                <?php if (!empty($college->parent_id)): ?>
                    <?= $this->render('partials/_main-campus', [
                        'college' => $parentCollege,
                    ]) ?>
                <?php endif; ?>
            </aside>
        </div>
    </div>

    <?php if ($liveApplicationForm && $isMobile && $college->is_sponsored == College::SPONSORED_NO): ?>
        <div id="liveApplicationForm"></div>
    <?php endif; ?>

    <?php if (!empty($faqs) && !$isMobile): ?>
        <?= $this->render('partials/_faq-card', [
            'faqs' => $faqs,
            'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' FAQs',
            'displayName' => (!empty($college->display_name) ? $college->display_name : $college->name),
            'pageName' => $pName ?? ''
        ]) ?>
    <?php endif; ?>

    <?php if (!empty($forums)): ?>
        <?= $this->render('partials/_forum-card', [
            'forums' => $forums,
            'viewAllUrl' => $college->slug
        ]) ?>
    <?php endif; ?>

    <!-- other colleges under university -->
    <?php if (!empty($affiliatedCollege) && !empty($parentCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Other Colleges under ' . (!empty($parentCollege['display_name']) ? $parentCollege['display_name'] : $parentCollege['name']),
            'colleges' => $affiliatedCollege,
            'city' => $city,
            'state' => $state
        ]) ?>
    <?php endif; ?>

    <?php if (!empty($collegeByDiscipline) && !empty($collegeByDiscipline['colleges']) && count($collegeByDiscipline['colleges']) > 2): ?>
        <?= $this->render('partials/_similar-college-card', [
            'collegeByDiscipline' => $collegeByDiscipline['colleges'],
            'sponsorStatus' => $collegeByDiscipline['sponsorStatus'],
            'college' => $college,
            'city' => $city,
            'state' => $state
        ]) ?>
    <?php endif; ?>

    <!-- Nearby colleges -->
    <?php if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Explore Nearby Colleges',
            'colleges' => $nearByCollege,
            'viewAllUrl' => !empty($city) ? "{$city->slug}" : '',
            'city' => $city,
            'state' => $state
        ]) ?>
    <?php endif; ?>

    <!-- Popular Colleges -->
    <?php if (!empty($popularCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Popular Colleges',
            'colleges' => $popularCollege,
            'city' => $city,
            'state' => $state
        ])
        ?>
    <?php endif; ?>

    <!-- related article -->
    <?php if (!empty($article)): ?>
        <?= $this->render('../partials/_productArticleCard', [
            'relatedArticles' => $article,
        ]); ?>
    <?php endif; ?>

    <!-- related News -->
    <?php if (!empty($news)): ?>
        <?= $this->render('../partials/_productNewsCard', [
            'news' => $news,
        ]); ?>
    <?php endif; ?>
</div>