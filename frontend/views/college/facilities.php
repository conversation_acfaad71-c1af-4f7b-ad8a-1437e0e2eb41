<?php

use common\models\College;
use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\LiveUpdate;
use common\services\CollegeService;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;

$defaultSeoInfo = ContentHelper::getCollegeDefaultSeoInfo(!empty($college->display_name) ? $college->display_name : $college->name, 'facilities', [], ['facilities' => $facilities]);
$defaultTitle = $college->display_name . ' Infrastructure: Campus, Hostel & Other Facilities';
$this->title = !empty($content->meta_title) ? CollegeHelper::parseContent($content->meta_title) : $defaultSeoInfo['title'];
$this->context->description = !empty($content->meta_description) ? CollegeHelper::parseContent($content->meta_description) : $defaultSeoInfo['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->context->ogImage = !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner();
$authorImage = !empty($content->author->profile->image) ? (Yii::getAlias('@profileDPFrontend') . '/' . $content->author->profile->image) : '/yas/images/usericon.png';

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$url = (new CollegeService)->checkFilterPageStatus([], !empty($city) ? $city->slug : '', '', '') == College::SPONSORED_NO ? '/all-colleges/' . (!empty($city) ? $city->slug : '') : '';
if (!empty($url) && !empty($city)) {
    $this->params['breadcrumbs'][] = ['label' => 'Colleges in ' . $city->name, 'url' => [$url], 'title' => 'Colleges in ' . $city->name];
}
$this->params['breadcrumbs'][] = ['label' => !empty($college->display_name) ? $college->display_name : $college->name, 'url' => [$college->slug], 'title' => !empty($college->display_name) ? $college->display_name : $college->name];
$this->params['breadcrumbs'][] = 'Facilities';

$this->registerLinkTag(['href' => !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
$this->registerLinkTag(['href' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'college-new.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');


// robots meta tag for no-index
if ($college->is_index == College::IS_NO_INDEX_NO) {
    $this->params['robots'] = 'noindex';
}

//gmu params
$this->params['entity'] = College::ENTITY_COLLEGE;
$this->params['course_count'] = !empty($courseCount) ? $courseCount : '';
$this->params['entity_id'] = $college->id ?? 0;
$this->params['interested_location'] = $city->id ?? null;
$this->params['entity_name'] = $college->name ?? '';
$this->params['entitySlug'] = $college->slug ?? '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = $college->display_name ?? '';
$this->params['pageName'] = 'facilities';

// schema
$this->params['schema'] = CollegeHelper::collegeSchema($college, $revCategoryRating, $reviewCount);
$courses = !empty($courses) ? CollegeHelper::getNonEmptyCourse($courses, 'facilities') : [];
?>

<div class="subPage">
    <!-- header -->
    <?= $this->render('partials/_header-new', [
        'menus' => $menus ?? [],
        'college' => $college,
        'content' => $content,
        'pageName' => 'facilities',
        'rating' => $revCategoryRating ? CollegeHelper::getTotalRating($revCategoryRating) : '',
        'reviewCount' => $reviewCount ?? '',
        'type' => $type,
        'approval' => $approval,
        'brochure' => $brochure,
        'heading' => !empty($content->h1) ? CollegeHelper::parseContent($content->h1) : (empty($defaultSeoInfo['h1']) ? $defaultSeoInfo['title'] : $defaultSeoInfo['h1']),
        'categoryRating' => $revCategoryRating,
        'city' => $city,
        'state' => $state,
        'author' => $authorDetail,
        'profile' => $profile,
        'dynamicCta' => $dynamicCta,
        'sponsorClientUrl' => !empty($sponsorClientUrl->redirection_link)  ? $sponsorClientUrl->redirection_link : '',
    ]) ?>

    <?php /*if (!empty($sponsorClientUrl->redirection_link)): ?>
        <?= $this->render('partials/_notification', [
            'sponsorClientUrl' => $sponsorClientUrl,
            'college' => $college,
        ]) ?>
    <?php endif;*/ ?>

    <!-- page specific navigation -->
    <?= $this->render('partials/_menu-card', [
        'menus' => $menus,
        'college' => $college,
        'pageName' => 'facilities',
        'dropdown' => $dropdowns
    ]) ?>

    <div class="row">
        <div class="col-md-8">
            <?php if (!empty($collegeNotificationUpdate)): ?>
                <div class="pageData <?= count($collegeNotificationUpdate) > 5  ? 'pageInfo' : '' ?>">
                    <div class="infoPage">
                        <?= $this->render('../partials/_collegeNotificationUpdate', [
                            'collegeNotificationUpdate' => $collegeNotificationUpdate,
                            'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Facilities',
                            'isShowTitle' => true
                        ]) ?>
                    </div>
                </div>
            <?php endif; ?>
            <?php if ($college->is_google_ads == College::ADS_ACTIVE && !$isMobile): ?>
                <aside>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                            <?php echo Ad::unit('GMU_COLLEGE_INFRASTRUCTURE_WEB_728x90_ATF', '[728,90]') ?>
                        </div>
                    </div>
                </aside>
            <?php endif;
            if (!empty($content->content)): ?>
                <?= $this->render('partials/_main-content-card', [
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Facilities',
                    'content' => CollegeHelper::parseContent(ContentHelper::htmlDecode($content->content)),
                    'facilities' => $facilities,
                    'contentAuthor' => $content,
                    'author' => $authorDetail,
                    'profile' => $profile,
                    'removeShowMore' => true,
                    'recentActivity' => $recentActivity,
                    'examContentTemplate' => $examContentTemplate,
                    'collegeNotificationUpdate' => $collegeNotificationUpdate,

                ]) ?>
            <?php endif; ?>

            <?php if (!empty($hostelData)): ?>
                <div class="pageData collegeRankings">
                    <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> Hostel Details</h2>
                    <table class="courseFeesTable rankTable">
                        <thead>
                            <tr>
                                <td>Hostel Details</td>
                                <td>Number of Hostel</td>
                                <td>Intake capacity</td>
                                <td>Number of Student Residing</td>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($hostelData as $key => $val): ?>
                                <tr>
                                    <td><?= $val['type'] ?></td>
                                    <td><?= $val['count'] ?></td>
                                    <td><?= $val['intake_capcity'] ?></td>
                                    <td><?= $val['student_residing'] ?></td>
                                </tr>
                            <?php endforeach; ?>

                        </tbody>
                    </table>
                </div>
            <?php endif; ?>

            <?php if ($liveApplicationForm && $isMobile && $college->is_sponsored == College::SPONSORED_NO): ?>
                <div id="liveApplicationForm"></div>
            <?php endif; ?>

            <?php /* if ($college->is_google_ads == College::ADS_ACTIVE && $isMobile): ?>
                <aside>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                            <?php echo Ad::unit('GMU_COLLEGE_INFRASTRUCTURE_WAP_300x100_ATF', '[300,100]') ?>
                        </div>
                    </div>
                </aside>
            <?php endif; */ ?>

            <?php /* if (isset($featuresGroup['Highlights'])): ?>
                <div class="pageData collegeHeighlights mobileOnly">
                    <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> Highlights</h2>
                    <table class="courseFeesTable">
                        <tbody>
                            <?php foreach ($featuresGroup['Highlights'] as $highlight): ?>
                                <?php if (isset(CollegeHelper::$collegeHighlights[$highlight['featureName']])): ?>
                                    <tr>
                                        <?php if (!empty($highlight['value'])): ?>
                                            <td><span class="spriteIcon <?= CollegeHelper::$collegeHighlights[$highlight['featureName']] ?>"></span> <?= $highlight['featureName'] ?></td>
                                            <td><?= ucfirst($highlight['value']) ?></td>
                                        <?php endif; ?>
                                    </tr>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif;*/ ?>
            <?php /*if (!empty($courses)): ?>
                <div class="pageData facilitiesTable courseAndFeeDiv mobileOnly">
                    <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> Check Fees Details</h2>
                    <table class="courseFeesTable">
                        <thead>
                            <tr>
                                <td>Course</td>
                                <td>Avg. Fees</td>
                                <td>Duration</td>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1;
                            foreach ($courses as $key => $value):
                                if ($count < 11):
                                    ?>
                                    <tr>
                                        <?php if (!empty($value['coursePage'])): ?>
                                            <td>
                                                <a href="<?= Url::base() . $value['coursePage'] ?>"><?= $value['short_name'] ?? $value['name'] ?></a>
                                            </td>
                                        <?php else: ?>
                                            <td>
                                                <a><?= $value['short_name'] ?? $value['name'] ?></a>
                                            </td>
                                        <?php endif; ?>
                                        <td>
                                            <?= !empty($value['avgFees']) ? '₹' . ContentHelper::indMoneyFormat($value['avgFees']) : '--' ?>
                                        </td>
                                        <td><?= !empty($value['duration']) ? CollegeHelper::yearsFormat($value['duration']) : '--' ?></td>

                                    </tr>
                                <?php endif;
                                $count++;
                            endforeach; ?>
                        </tbody>
                    </table>
                    <?php if (!is_numeric($menus['courses-fees'])): ?>
                        <div class="moreCoursesLinkContainer">
                            <a class="moreCoursesLink" title="<?= CollegeHelper::subPageTitle($college, 'courses-fees') ?>" href="<?= Url::toCollege($college->slug, 'courses-fees') ?>">
                                More Courses<span class="spriteIcon urlIcon"></span>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; */ ?>

            <!-- Faq -->
            <?php if (!empty($faqs)): ?>
                <?= $this->render('partials/_faq-card', [
                    'faqs' => $faqs,
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Facilities FAQs',
                    'displayName' => (!empty($college->display_name) ? $college->display_name : $college->name),
                    'pageName' => 'Facilities'
                ]) ?>
            <?php endif; ?>

            <div class="removeFixedQuickLink">
                <!-- Do not Delete this -->
            </div>

            <!-- reviews widget -->
            <?php if (!empty($reviews)): ?>
                <?= $this->render('partials/_review-card', [
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Reviews',
                    'reviews' => $reviews,
                    'distrbutionRating' => $revDistributionRating,
                    'categoryRating' => $revCategoryRating,
                    'collegeId' => $college->id,
                    'viewAllURL' => !empty($menus) && !is_numeric($menus['reviews']) ? Url::toCollege($college->slug, 'reviews') : '',
                    'reviewCount' => $reviewCount ?? '',
                    'category' => 4
                ]) ?>
            <?php endif; ?>
        </div>

        <div class="col-md-4 noSticky">
            <aside>
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="getSupport">
                        <!-- <div class="row">
                            <img class="lazyload" loading="lazy" width="80" height="80" data-src="/yas/images/bulbIcon.svg" src="/yas/images/bulbIcon.svg" alt="">
                            <p>Are You Interested In This College?</p>
                        </div> -->
                        <div class="lead-cta lead-cta-cls-button" data-lead_cta="0" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>"></div>
                    </div>
                <?php endif; ?>
                <?php if (!empty($college->parent_id)): ?>
                    <?= $this->render('partials/_main-campus', [
                        'college' => $parentCollege,
                    ]) ?>
                <?php endif; ?>

                <?php if ($liveApplicationForm && !$isMobile && $college->is_sponsored == College::SPONSORED_NO): ?>
                    <div id="liveApplicationForm"></div>
                <?php endif; ?>

                <?php /* if (!empty($featuredNews) || !empty($recentNews)): ?>
                    <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                        'featured' => $featuredNews,
                        'recents' => $recentNews,
                        'isAmp'  => 0,
                        'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                        'smallIcone' => 1
                    ]); ?>
                <?php endif; ?>

                <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                    <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                        'trendings' => $trendingArticles,
                        'recentArticles' => $recentArticles,
                    ]); ?>
                <?php endif;*/ ?>

            </aside>
            <?php if ($college->is_google_ads == College::ADS_ACTIVE): ?>
                <?= $this->render('partials/_sidebar-ads.php', [
                    'class' => 'desktopOnly',
                    'ads' => [
                        ['slot' => 'GMU_COLLEGE_INFRASTRUCTURE_WEB_300x250_MTF_1', 'size' => '[300, 250]', 'isMobile' => false],
                        ['slot' => 'GMU_COLLEGE_INFRASTRUCTURE_WEB_300x250_MTF_2', 'size' => '[300, 250]', 'isMobile' => false]
                    ]
                ]) ?>
            <?php endif; ?>
        </div>
    </div>

    <?php if (!empty($forums)): ?>
        <?= $this->render('partials/_forum-card', [
            'forums' => $forums,
            'viewAllUrl' => $college->slug
        ]) ?>
    <?php endif; ?>

    <!-- other colleges under university -->
    <?php if (!empty($affiliatedCollege) && !empty($parentCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Other Colleges under ' . (!empty($parentCollege['display_name']) ? $parentCollege['display_name'] : $parentCollege['name']),
            'colleges' => $affiliatedCollege,
            'city' => $city,
            'state' => $state,
        ]) ?>
    <?php endif; ?>

    <?php if (!empty($collegeByDiscipline) && !empty($collegeByDiscipline['colleges']) && count($collegeByDiscipline['colleges']) > 2): ?>
        <?= $this->render('partials/_similar-college-card', [
            'collegeByDiscipline' => $collegeByDiscipline['colleges'],
            'sponsorStatus' => $collegeByDiscipline['sponsorStatus'],
            'college' => $college
        ]) ?>
    <?php endif; ?>

    <!-- Nearby colleges -->
    <?php if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Explore Nearby Colleges',
            'colleges' => $nearByCollege,
            'viewAllUrl' => !empty($city) ? "{$city->slug}" : '',
            'city' => $city,
            'state' => $state,
        ]) ?>
    <?php endif; ?>

    <!-- Popular Colleges -->
    <?php if (!empty($popularCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Popular Colleges',
            'colleges' => $popularCollege,
            'city' => $city,
            'state' => $state,
        ])
        ?>
    <?php endif; ?>

    <!-- related article -->
    <?php if (!empty($article)): ?>
        <?= $this->render('../partials/_productArticleCard', [
            'relatedArticles' => $article,
        ]); ?>
    <?php endif; ?>

    <!-- related News -->
    <?php if (!empty($news)): ?>
        <?= $this->render('../partials/_productNewsCard', [
            'news' => $news,
        ]); ?>
    <?php endif; ?>

    <!-- advertisement -->
    <?php if ($college->is_google_ads == College::ADS_ACTIVE): ?>
        <aside>
            <div class="horizontalRectangle">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php if ($isMobile): ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250') ?>
                    <?php else: ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90') ?>
                    <?php endif; ?>
                </div>
            </div>
        </aside>
    <?php endif; ?>

    <?php /*<nav class="breadcrumbDiv">
        <div class="container">
            <ul>
                <li><a href="/" class="spriteIcon homeIcon" title="Home"></a></li>
                <li><a href="<?= '/all-colleges/' . $college->city->slug?>" title="<?= 'Colleges in ' . $college->city->name ?>">Colleges in <?= $college->city->name ?></a></li>
                <li><a href="<?= Url::toCollege($college->slug) ?>" title="<?= !empty($college->display_name) ? $college->display_name : $college->name ?>"><?= !empty($college->display_name) ? $college->display_name : $college->name ?></a></li>
                <li>Facilities</li>
            </ul>
        </div>
    </nav>*/ ?>
</div>
<?php /* if (!empty($sponsorClientUrl->redirection_link)):?>
    <?= $this->render('partials/_apply-now', [
        'sponsorClientUrl' => $sponsorClientUrl,
        'college' => $college
    ]) ?>
<?php  endif;*/ ?>