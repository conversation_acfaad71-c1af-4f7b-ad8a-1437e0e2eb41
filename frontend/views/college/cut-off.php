<?php

use common\models\College;
use common\helpers\ContentHelper;
use common\helpers\CollegeHelper;
use common\helpers\DataHelper;
use common\models\LiveUpdate;
use common\services\CollegeService;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;

if (empty($subPageType)) {
    $defaultSeoInfo = ContentHelper::getCollegeDefaultSeoInfo(!empty($college->display_name) ? $college->display_name : $college->name, 'cut-off', [], ['year' => $cutOff['searchModel']->years]);
} else {
    $defaultSeoInfo = ContentHelper::getCollegeDefaultSubPageSeoInfo(!empty($college->display_name) ? $college->display_name : $college->name, 'cut-off', $subPageType[0]);
}
$defaultTitle = $college->display_name . ' Cut off ' . date('Y') . ', ' . date('Y', strtotime('-1 year')) . ', ' . date('Y', strtotime('-2 year')) . ': Check Previous Year Cut Off Ranks';
$this->title = !empty($content->meta_title) ? CollegeHelper::parseContent($content->meta_title) : $defaultSeoInfo['title'];
// $this->context->description = 'Check ' . $college->display_name . ' course & exam-wise previous year cut off for ' . date('Y') . ', ' . date('Y', strtotime('-1 year')) . ', ' . date('Y', strtotime('-2 year')) . ' with closing score/rank trends.';
$this->context->description = !empty($content->meta_description) ? CollegeHelper::parseContent($content->meta_description) : $defaultSeoInfo['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->context->ogImage = !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner();

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$url = (new CollegeService)->checkFilterPageStatus([], !empty($city) ? $city->slug : '', '', '') == College::SPONSORED_NO ? '/all-colleges/' . (!empty($city) ? $city->slug : '') : '';
if (!empty($url) && !empty($city)) {
    $this->params['breadcrumbs'][] = ['label' => 'Colleges in ' . $city->name, 'url' => [$url], 'title' => 'Colleges in ' . $city->name];
}
$this->params['breadcrumbs'][] = ['label' => !empty($college->display_name) ? $college->display_name : $college->name, 'url' => [$college->slug], 'title' => !empty($college->display_name) ? $college->display_name : $college->name];
if (!empty($subPageType)) {
    $cutOffSlug = 'college/' . $college->slug . '-cut-off';
    $this->params['breadcrumbs'][] = ['label' => 'Cutoff', 'url' => [$cutOffSlug], 'title' => 'Cutoff'];
    $this->params['breadcrumbs'][] = $faqPageName = $subPageType[0] . ' Cutoff';
} else {
    $this->params['breadcrumbs'][] = $faqPageName = 'Cutoff';
}

$this->registerLinkTag(['href' => !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
$this->registerLinkTag(['href' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'college-new.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');

$canonicalUrl = Url::base(true) . '/' . \Yii::$app->request->getPathInfo();
$this->params['canonicalUrl'] = $canonicalUrl;

// robots meta tag for no-index
if ($college->is_index == College::IS_NO_INDEX_NO) {
    $this->params['robots'] = 'noindex';
}

//gmu params
$this->params['entity'] = College::ENTITY_COLLEGE;
$this->params['course_count'] = !empty($courseCount) ? $courseCount : '';
$this->params['entity_id'] = $college->id ?? 0;
$this->params['interested_location'] = $city->id ?? null;
$this->params['entity_name'] = $college->name ?? '';
$this->params['entitySlug'] = $college->slug ?? '';
$this->params['dynamicCta'] = empty($dynmicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = $college->display_name ?? '';
if (!empty($subPageType)) {
    $this->params['pageName'] = 'cut_off_' . strtolower(str_replace(' ', '_', $subPageType[1]));
} else {
    $this->params['pageName'] = 'cut-off';
}
$this->params['canonicalUrl'] = $canonicalUrl;
// schema
$this->params['schema'] = CollegeHelper::collegeSchema($college, $revCategoryRating, $reviewCount);
?>

<div class="collegeCutoffPage">

    <?= $this->render('partials/_header-new', [
        'menus' => $menus ?? [],
        'college' => $college,
        'content' => $content,
        'pageName' => 'cut-off',
        'rating' => $revCategoryRating ? CollegeHelper::getTotalRating($revCategoryRating) : '',
        'reviewCount' => $reviewCount ?? '',
        'type' => $type,
        'approval' => $approval,
        'brochure' => $brochure,
        'heading' => !empty($content->h1) ? CollegeHelper::parseContent($content->h1) : (empty($defaultSeoInfo['h1']) ? $defaultSeoInfo['title'] : $defaultSeoInfo['h1']),
        'defaultTitle' => $defaultTitle,
        'categoryRating' => $revCategoryRating,
        'city' => $city,
        'state' => $state,
        'author' => $authorDetail,
        'profile' => $profile,
        'dynamicCta' => isset($dynamicCta) && !empty($dynamicCta) ? $dynamicCta : [],
        'sponsorClientUrl' => !empty($sponsorClientUrl->redirection_link)  ? $sponsorClientUrl->redirection_link : '',
    ]) ?>

    <?php /*if (!empty($sponsorClientUrl->redirection_link)):?>
        <?= $this->render('partials/_notification', [
            'sponsorClientUrl' => $sponsorClientUrl,
            'college' => $college,
        ]) ?>
    <?php endif;*/ ?>

    <?= $this->render('partials/_menu-card', [
        'menus' => $menus,
        'college' => $college,
        'pageName' => 'cut-off',
        'dropdown' => $dropdowns,
        'type' => empty($subPageType) ? null : $subPageType[0]
    ]) ?>

    <div class="row">
        <div class="col-md-8">

            <?php if (!empty($collegeNotificationUpdate)): ?>
                <div class="pageData <?= count($collegeNotificationUpdate) > 5  ? 'pageInfo' : '' ?>">
                    <div class="infoPage">
                        <?= $this->render('../partials/_collegeNotificationUpdate', [
                            'collegeNotificationUpdate' => $collegeNotificationUpdate,
                            'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' About',
                            'isShowTitle' => true
                        ]) ?>
                    </div>
                </div>
            <?php endif; ?>
            <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU() && !$isMobile): ?>
                <aside>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                            <?php echo Ad::unit('GMU_COLLEGE_CUT_OFF_WEB_728x90_ATF', '[728,90]') ?>
                        </div>
                    </div>
                </aside>
            <?php endif; ?>
            <?php if (!empty($courseCutOffData)): ?>
                <div class="pageData cutOffDiv">
                    <h2><?= $college->display_name ?> Cutoff <?= $courseCutOffData['year'] ?> (General, Gender - Gender Neutral)</h2>
                    <?= $this->render('partials/_author-detail-mobile', [
                        'content' => $content,
                        'author' => $authorDetail,
                        'profile' => $profile
                    ]) ?>
                    <?php if (!empty($courseCutOffData['opclArr'])): ?>
                        <table>
                            <thead>
                                <tr>
                                    <td>Specialization Name</td>
                                    <td>Opening Rank</td>
                                    <td>Closing Rank</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <?php
                                    array_multisort(array_column($courseCutOffData['opclArr'], 'open_rank'), SORT_ASC, $courseCutOffData['opclArr']);
                                    foreach ($courseCutOffData['opclArr'] as $ks => $sc): ?>
                                <tr>
                                    <td><?= $sc['courseName'] ?></td>
                                    <td><?= $sc['open_rank'] ?></td>
                                    <td><?= $sc['close_rank'] ?></td>
                                </tr>
                                    <?php endforeach; ?>
                            </tbody>
                            </tr>
                            </tbody>
                        </table>
                        <div class="cutOffNote">
                            Note: This result are for <?= $courseCutOffData['opclArr_examName'] ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($courseCutOffData['percentileArr'])): ?>
                        <table>
                            <thead>
                                <tr>
                                    <td>Specialization Name</td>
                                    <td>Percentile</td>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                array_multisort(array_column($courseCutOffData['percentileArr'], 'percentile'), SORT_ASC, $courseCutOffData['percentileArr']);
                                foreach ($courseCutOffData['percentileArr'] as $ks => $sc): ?>
                                    <tr>
                                        <td><?= $sc['courseName'] ?></td>
                                        <td><?= $sc['percentile'] ?> %</td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        <div class="cutOffNote">
                            Note: This result are for <?= $courseCutOffData['percentileArr_examName'] ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($courseCutOffData['scoreArr'])): ?>
                        <table>
                            <thead>
                                <tr>
                                    <td>Specialization Name</td>
                                    <td>Closing Score</td>
                                </tr>
                            </thead>
                            <tbody>
                                <?php array_multisort(array_column($courseCutOffData['scoreArr'], 'closing_score'), SORT_ASC, $courseCutOffData['scoreArr']);
                                foreach ($courseCutOffData['scoreArr'] as $ks => $sc): ?>
                                    <tr>
                                        <td><?= $sc['courseName'] ?></td>
                                        <td><?= $sc['closing_score'] ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        <div class="cutOffNote">
                            Note: This result are for <?= $courseCutOffData['scoreArr_examName'] ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <?php /* if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU() && $isMobile): ?>
                <aside>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                            <?php if ($isMobile): ?>
                                <?php echo Ad::unit('GMU_COLLEGE_CUT_OFF_WAP_300x100_ATF', '[300,100]') ?>
                            <?php else: ?>
                                <?php echo Ad::unit('GMU_COLLEGE_CUT_OFF_WEB_728x90_ATF', '[728,90]') ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </aside>
            <?php endif; */ ?>

            <?php if (!empty($cutOff['dataProvider']->allModels)): ?>
                <section class="filterSection filtersAll cutoffFilterSection">
                    <?= $this->render('partials/_cut-off-filter', [
                        'model' => $cutOff['searchModel'],
                        'college' => $college,
                        'content' => $content->content ?? [],
                        'author' => $authorDetail ?? [],
                        'profile' => $profile ?? [],
                        'contentAuthor' => $content ?? [],
                    ]) ?>
                </section>

                <section class="cutOffDetailSection">
                    <div class="courseCutOffTypeList">
                        <?php if (!empty($cutOff['dataProvider']->allModels)): ?>
                            <?= $this->render(
                                'partials/_cut-off-list',
                                [
                                    'models' => $cutOff['dataProvider']->allModels,
                                    'college' => $college,
                                    'state' => $state,
                                    'isMobile' => $isMobile,
                                    'selectedCategory' => $cutOff['selectedCategory'],
                                ]
                            ); ?>
                        <?php else: ?>
                            <div> NO Result Found</div>
                        <?php endif; ?>
                    </div>
                </section>
            <?php endif; ?>
            <?php if (!empty($content->content) && !empty($cutOff['dataProvider']->allModels)): ?>
                <?= $this->render('partials/_main-content-card', [
                    'content' => CollegeHelper::parseContent(ContentHelper::htmlDecode($content->content)),
                    'contentAuthor' => [],
                    'author' => [],
                    'profile' => [],
                    'recentActivity' => $recentActivity,
                    'removeShowMore' => 'yes',
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' About',
                    'examContentTemplate' => $examContentTemplate,
                    'collegeNotificationUpdate' => $collegeNotificationUpdate,

                ]); ?>
            <?php elseif (!empty($content->content)):  ?>
                <?= $this->render('partials/_main-content-card', [
                    'content' => CollegeHelper::parseContent(ContentHelper::htmlDecode($content->content)),
                    'contentAuthor' => $content,
                    'author' => $authorDetail,
                    'profile' => $profile,
                    'recentActivity' => $recentActivity,
                    'removeShowMore' => 'yes',
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' About',
                    'examContentTemplate' => $examContentTemplate,
                    'collegeNotificationUpdate' => $collegeNotificationUpdate,

                ]); ?>
            <?php endif; ?>
            <?php /*elseif (!empty($parentContent->content)): ?>
                <?= $this->render('partials/_main-content-card', [
                    'content' => CollegeHelper::parseContent(ContentHelper::htmlDecode($content->content)),
                    // 'contentAuthor' => $content,
                    // 'author' => $authorDetail,
                    // 'profile' => $profile,
                    'recentActivity' => $recentActivity,
                    'removeShowMore' => 'yes',
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' About',
                ]);*/ ?>

            <?php if (!empty($faqs)): ?>
                <?= $this->render('partials/_faq-card', [
                    'faqs' => $faqs,
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Cutoff FAQs',
                    'displayName' => (!empty($college->display_name) ? $college->display_name : $college->name),
                    'pageName' => $faqPageName
                ]) ?>
            <?php endif; ?>

            <div class="removeFixedQuickLink">
                <!-- Do not Delete this -->
            </div>
            <?php if (!empty($reviews)): ?>
                <?= $this->render('partials/_review-card', [
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Reviews',
                    'reviews' => $reviews,
                    'distrbutionRating' => $revDistributionRating,
                    'categoryRating' => $revCategoryRating,
                    'collegeId' => $college->id,
                    'viewAllURL' => !empty($menus) && !is_numeric($menus['reviews']) ? Url::toCollege($college->slug, 'reviews') : '',
                    'reviewCount' => $reviewCount ?? '',
                    'category' => 3
                ]) ?>
            <?php endif; ?>
        </div>
        <div class="col-md-4">
            <aside>
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="getSupport">
                        <!-- <div class="row">
                            <img class="lazyload" loading="lazy" width="80" height="80" data-src="/yas/images/bulbIcon.svg" src="/yas/images/bulbIcon.svg" alt="">
                            <p>Are You Interested In This College?</p>
                        </div> -->
                        <div class="lead-cta lead-cta-cls-button" data-lead_cta="0" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>"></div>
                    </div>
                <?php endif; ?>
                <?php if (!empty($college->parent_id)): ?>
                    <?= $this->render('partials/_main-campus', [
                        'college' => $parentCollege,
                    ]) ?>
                <?php endif; ?>

                <?php if ($liveApplicationForm && $college->is_sponsored == College::SPONSORED_NO && Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div id="liveApplicationForm"></div>
                <?php endif; ?>

                <?php /*if (!empty($featuredNews) || !empty($recentNews)): ?>
                    <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                        'featured' => $featuredNews,
                        'recents' => $recentNews,
                        'isAmp'  => 0,
                        'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                        'smallIcone' => 1
                    ]); ?>
                <?php endif; ?>

                <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                    <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                        'trendings' => $trendingArticles,
                        'recentArticles' => $recentArticles,
                    ]); ?>
                <?php endif;*/ ?>

            </aside>
            <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
                <?= $this->render('partials/_sidebar-ads.php', [
                    'class' => 'desktopOnly',
                    'ads' => [
                        ['slot' => 'GMU_COLLEGE_CUT_OFF_WEB_300x250_MTF_1', 'size' => '[300, 250]', 'isMobile' => false],
                        ['slot' => 'GMU_COLLEGE_CUT_OFF_WEB_300x250_MTF_2', 'size' => '[300, 250]', 'isMobile' => false]
                    ]
                ]) ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- other colleges under university -->
    <?php if (!empty($affiliatedCollege) && !empty($parentCollege) && !empty($parentCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Other Colleges under ' . (!empty($parentCollege['display_name']) ? $parentCollege['display_name'] : $parentCollege['name']),
            'colleges' => $affiliatedCollege,
            'city' => $city,
            'state' => $state
        ]) ?>
    <?php endif; ?>

    <?php if (!empty($collegeByDiscipline) && !empty($collegeByDiscipline['colleges']) && count($collegeByDiscipline['colleges']) > 2): ?>
        <?= $this->render('partials/_similar-college-card', [
            'collegeByDiscipline' => $collegeByDiscipline['colleges'],
            'sponsorStatus' => $collegeByDiscipline['sponsorStatus'],
            'college' => $college
        ]) ?>
    <?php endif; ?>

    <?php if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Explore Nearby Colleges',
            'colleges' => $nearByCollege,
            'viewAllUrl' => !empty($city) ? "{$city->slug}" : '',
            'city' => $city,
            'state' => $state
        ]) ?>
    <?php endif; ?>


    <!-- Popular Colleges -->
    <?php if (!empty($popularCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Popular Colleges',
            'colleges' => $popularCollege,
            'city' => $city,
            'state' => $state
        ])
        ?>
    <?php endif; ?>

    <!-- related article -->
    <?php if (!empty($article)): ?>
        <?= $this->render('../partials/_productArticleCard', [
            'relatedArticles' => $article,
        ]); ?>
    <?php endif; ?>

    <!-- related News -->
    <?php if (!empty($news)): ?>
        <?= $this->render('../partials/_productNewsCard', [
            'news' => $news,
        ]); ?>
    <?php endif;

    if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
        <aside>
            <div class="horizontalRectangle">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php if ($isMobile): ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250') ?>
                    <?php else: ?>
                        <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90') ?>
                    <?php endif; ?>
                </div>
            </div>
        </aside>
    <?php endif; ?>
</div>
<?php /* if (!empty($sponsorClientUrl->redirection_link)):?>
    <?= $this->render('partials/_apply-now', [
        'sponsorClientUrl' => $sponsorClientUrl,
        'college' => $college
    ]) ?>
<?php  endif;*/ ?>