<?php

use common\models\College;
use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\helpers\CourseHelper;
use common\helpers\DataHelper;
use common\models\CollegeCourse;
use common\models\Lead;
use frontend\helpers\Lead as HelpersLead;
use common\models\LiveUpdate;
use common\models\Program;
use common\services\CollegeService;
use common\services\UserService;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use yii\helpers\StringHelper;

$defaultSeoInfo = ContentHelper::getCollegeDefaultSeoInfo(
    !empty($college->display_name) ? $college->display_name : $college->name,
    'course',
    $menus,
    ['fees-course-summary' => !empty($specializationList) ? $specializationList : []],
    $course->short_name
);

$examData = [];
if (!empty($programExams)) {
    $examData = CollegeHelper::formateDate($programExams);
}

$this->title = !empty($content->meta_title) ? CollegeHelper::parseContent($content->meta_title) : $defaultSeoInfo['title'];
$this->context->description = !empty($content->meta_description) ? CollegeHelper::parseContent($content->meta_description) : $defaultSeoInfo['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->context->ogImage = !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner();

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$url = (new CollegeService)->checkFilterPageStatus([], !empty($city) ? $city->slug : '', '', '') == College::SPONSORED_NO ? '/all-colleges/' . (!empty($city) ? $city->slug : '') : '';
if (!empty($url) && !empty($city)) {
    $this->params['breadcrumbs'][] = ['label' => 'Colleges in ' . $city->name, 'url' => [$url], 'title' => 'Colleges in ' . $city->name];
}
$this->params['breadcrumbs'][] = ['label' =>  !empty($college->display_name) ? $college->display_name : $college->name, 'url' => ['/college/' . $college->slug], 'title' =>  !empty($college->display_name) ? $college->display_name : $college->name];
if (!is_numeric($menus['courses-fees'])) {
    $this->params['breadcrumbs'][] = ['label' => 'Courses and Fees', 'url' => ['/college/' . $college->slug . '-courses-fees'], 'title' => 'Courses and Fees'];
}
$this->params['breadcrumbs'][] = $course->short_name . ' at ' . (!empty($college->display_name) ? $college->display_name : $college->name) ?? 'Detailed Fees Structure and Eligibility Criteria';

$this->registerLinkTag(['href' => !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);
$this->registerLinkTag(['href' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(), 'rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high']);

// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'college-new.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');

$this->params['schema'] = CollegeHelper::collegeSchema($college, $revCategoryRating, $reviewCount);

// robots meta tag for no-index
if ($college->is_index == College::IS_NO_INDEX_NO) {
    $this->params['robots'] = 'noindex';
}

//gmu params
$this->params['entity'] = College::ENTITY_COLLEGE;
$this->params['course_count'] = !empty($courseCount) ? $courseCount : '';
$this->params['entity_id'] = $college->id ?? 0;
$this->params['interested_location'] = $city->id ?? null;
$this->params['entity_name'] = $college->name ?? '';
$this->params['entitySlug'] = $college->slug ?? '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = $college->display_name ?? '';
$this->params['pageName'] = 'ci';
$this->params['course_id'] = $course->id;
?>
<!-- <div class="blueBgDiv mobileOnly"> -->
<!-- do not delete this -->
<!-- </div> -->

<div class="programPage">

    <?= $this->render('partials/_header-new', [
        'menus' => $menus ?? [],
        'college' => $college,
        'accredited' => $accredited,
        'recognised' => $recognised,
        'rating' => $revCategoryRating ? CollegeHelper::getTotalRating($revCategoryRating) : '',
        'reviewCount' => $reviewCount ?? '',
        'type' => $type,
        'approval' => $approval,
        'brochure' => $brochure ?? [],
        'heading' => !empty($content->h1) ? CollegeHelper::parseContent($content->h1) : (empty($defaultSeoInfo['h1']) ? $defaultSeoInfo['title'] : $defaultSeoInfo['h1']),
        'categoryRating' => $revCategoryRating,
        'content' => $content,
        'pageName' => 'ci',
        'page_slug' => $course->slug ?? '',
        'city' => $city,
        'state' => $state,
        'dynamicCta' => $dynamicCta ?? [],
        'sponsorClientUrl' => !empty($sponsorClientUrl->redirection_link)  ? $sponsorClientUrl->redirection_link : '',
        'author' => !empty($author) ? $author : []
    ]) ?>

    <?= $this->render('partials/_menu-card', [
        'menus' => $menus,
        'college' => $college,
        'pageName' => 'ci',
        'dropdown' => $dropdowns,

    ]) ?>

    <div class="row row-cls">
        <div class="col-md-8">
            <?php if (!empty($specializationList)): ?>
                <div class=" pageData <?= count($specializationList) > 5 ? 'pageInfo' : '' ?> courseAndFeeDiv ciPageTable">
                    <?php if ($this->params['pageName'] == 'ci') { ?>
                        <?= $this->render('partials/_author-detail-mobile', [
                            'content' => $content,
                            'author'=> !empty($author) ? $author : []
                        ])
                        ?>
                    <?php } ?>

                    <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> <?= $course->short_name ?> Fees & Courses List</h2>
                    <table>
                        <thead>
                            <tr>
                                <td>Specialization</td>
                                <td>Duration</td>
                                <td>Tuition Fees</td>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($specializationList as $list): ?>
                                <tr>
                                    <td>
                                        <a href="<?= Url::toCollegeCourseProgram($college->slug, $list['slug'], $list['pageIndex']) ?>" title="<?= $college->display_name ?? $college->name ?> <?= $list['name'] ?? $course->short_name ?>">
                                            <?= $list['name'] ?>
                                        </a>
                                    </td>
                                    <td><?= !empty($list['duration']) ? CollegeHelper::yearsFormatCourses($list['duration'], !empty($list['duration_type']) ? CollegeHelper::$programDurationType[$list['duration_type']] : 'Months') : '--' ?></td>
                                    <td><?= !empty($list['fees']) ? '₹' . CollegeHelper::feesFormat($list['fees']) :
                                            frontend\helpers\Html::leadButton(
                                                'Get Fees',
                                                [
                                                    'entity' => Lead::ENTITY_COLLEGE,
                                                    'entityId' => $college->id,
                                                    'stateId' => !empty($state) ? $state->id : '',
                                                    'interestedLocation' => $college->city_id,
                                                    'ctaLocation' => $isMobile ? UserService::parseDynamicCta('colleges_course_information_wap_lead_{slug}_card_mtf_right_cta6', '', $list['slug']) : UserService::parseDynamicCta('colleges_course_information_web_lead_{slug}_card_mtf_right_cta6', '', $list['slug']),
                                                    'ctaText' => 'Get Fees',
                                                    'leadformtitle' => 'Register now to get Detailed Fees',
                                                    'subheadingtext' => 'Get latest updates and access to premium content',
                                                    'image' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(),
                                                ],
                                                ['class' => 'getLeadForm textBlue getFees']
                                            ) ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>

            <?php if (!empty($course)):
                $courseDetails = isset($otherCourses[$course->name]) ? $otherCourses[$course->name] : '';
                ?>
                <div class="pageData collegeHeighlights">
                    <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> <?= $course->short_name ?? $course->name ?> Course Highlights</h2>
                    <table class="courseFeesTable collegeCourseTable">
                        <tbody>
                            <tr>
                                <td style="font-weight:400;"><span class="spriteIcon courseHighlightIcon courseHighlight1"></span> Course Name
                                </td>
                                <td><?= $course->short_name ?? $course->name ?></td>
                            </tr>
                            <?php if (isset(CourseHelper::$degree[$course->degree])): ?>
                                <tr>
                                    <td style="font-weight:400;"><span class="spriteIcon courseHighlightIcon courseHighlight1"></span> Course Level
                                    </td>
                                    <td><?= CourseHelper::$degree[$course->degree] ?> Program</td>
                                </tr>
                            <?php endif; ?>
                            <?php if (!empty($courseDetails['course_duration'])): ?>
                                <tr>
                                    <td style="font-weight:400;"><span class="spriteIcon courseHighlightIcon courseHighlight2"></span> Duration</td>
                                    <td><?= CollegeHelper::yearsFormat($courseDetails['course_duration']) ?></td>
                                </tr>
                            <?php endif; ?>
                            <?php if (!empty($collegeExams)): ?>
                                <tr>
                                    <td style="font-weight:400;"><span class="spriteIcon courseHighlightIcon courseHighlight3"></span> Eligibility/Entrance Exams Accepted</td>
                                    <td><?= implode(', ', array_slice(array_keys($collegeExams), 0, 2)) ?>
                                        <?php if (count($collegeExams) > 2): ?>
                                            <span class="programHideExam">, <?= implode(', ', array_slice(array_keys($collegeExams), 2)) ?></span>
                                            <span class="textBlue programExamCount">+<?= count($collegeExams) - 2  ?> More</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endif; ?>
                            <?php if (!empty($courseDetails['avgFees'])): ?>
                                <tr>
                                    <td style="font-weight:400;"><span class="spriteIcon courseHighlightIcon courseHighlight5"></span>Average Fees</td>
                                    <td>₹ <?= ContentHelper::indMoneyFormat($courseDetails['avgFees']) ?></td>
                                </tr>
                            <?php endif; ?>
                            <?php if (!empty($courseDetails['mode']) && !empty(DataHelper::$modeArr[$courseDetails['mode']])): ?>
                                <tr>
                                    <td style="font-weight:400;"><span class="spriteIcon courseHighlightIcon courseHighlight4"></span> Mode</td>
                                    <td><?= DataHelper::$modeArr[$courseDetails['mode']] ?></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>

            <?php if (!empty($studentDiversityData)): ?>
                <div class="pageData collegeRankings">
                    <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?></h2>
                    <table class="courseFeesTable rankTable">
                        <thead>
                            <tr>
                                <td>Category</td>
                                <?php if (!empty($studentDiversityData['category'])): ?>
                                    <?php foreach ($studentDiversityData['category'] as $key): ?>
                                        <td><?= $key ?></td>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $countCategory = count($studentDiversityData['category']);
                            unset($studentDiversityData['category']);
                            foreach ($studentDiversityData as $key => $val): ?>
                                <tr>
                                    <td><?= ($key == 'All') ? 'Total' : $key ?></td>
                                    <?php
                                    $rowcount = count($val);
                                    $leftColumn = $countCategory - $rowcount;
                                    foreach ($val as $k => $v): ?>
                                        <td><?= !empty($v) ?  $v :  '-' ?></td>
                                    <?php endforeach;
                                    if ($leftColumn !== 0):
                                        for ($i = 0; $i < $leftColumn; $i++):
                                            ?>
                                            <td style="text-align: center;">-</td>
                                        <?php endfor;
                                    endif;
                                    ?>
                                </tr>
                            <?php endforeach; ?>

                        </tbody>
                    </table>
                </div>
            <?php endif; ?>

            <?php if (!empty($getCiSubpage)): ?>
                <?= $this->render('partials/ci_pi_subpage_related_links', [
                    'getCiSubpage' => $getCiSubpage,
                    'college_slug' => $college->slug,
                    'college_name' => $college->display_name ?? $college->name,
                    'page_name' => $course->name,
                    'page_slug' => $course->slug,
                    'page_id' =>  $content->id,
                    'page' => 'ci'
                ]) ?>
            <?php endif; ?>

            <?php if (isset($content->qualification)): ?>
                <?php if (!empty(json_decode($content->qualification)[0]->educational_qualification)): ?>
                    <div class="pageData">
                        <h2><?= !empty($college->display_name) ? $college->display_name : $college->name ?> <?= $course->short_name ?> Eligibility</h2>
                        <table>
                            <tbody>
                                <tr>
                                    <th>Educational Qualification</th>
                                    <th>Required Eligibility</th>
                                </tr>
                                <tr>
                                    <?php
                                    $details = json_decode($content->qualification);
                                    if (!empty($details)):
                                        foreach ($details as $key => $value):
                                            $qualification = (array) $value;
                                            $qualificationValue = array_values($qualification);
                                            ?>
                                <tr>
                                    <td><?= $qualificationValue[0] ?></td>
                                    <td> <?= $qualificationValue[2] . ' ' . $qualificationValue[1] ?></td>
                                </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                        </tr>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <?php if (!empty($cutOff['dataProvider']->allModels) && array_key_exists($course->name, $cutOff['dataProvider']->allModels)): ?>
                <?= $this->render('partials/_college_course_cutoff', [
                    'cutOff' => $cutOff,
                    'college' => $college,
                    'course_id' => $course->id,
                    'models' => $cutOff['dataProvider']->allModels,
                    'state' => $state,
                    'isMobile' => $isMobile,
                    'page' => 'ci'
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($dates)): ?>
                <?= $this->render('partials/course_program_dates', [
                    'dates' => $dates,
                    'title' => $course->short_name,
                    'college' => $college,
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($streamRanks)): ?>
                <?= $this->render('partials/_course_program_ranking', [
                    'streamRanks' => $streamRanks,
                    'title' => $course->short_name,
                    'college' => $college,
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($examData)): ?>
                <?= $this->render('partials/_course_program_imp_dates_exams', [
                    'examData' => $examData,
                    'title' => $course->short_name,
                    'college' => $college,
                ]) ?>
            <?php endif; ?>


            <?php if (!empty($content)): ?>
                <?= $this->render('partials/_course-content', [
                    'courseName' => $course->short_name,
                    'content' => $content,
                    'college' => $college,
                ]) ?>
            <?php endif; ?>
            <?php if (!empty($faqs) && $isMobile): ?>
                <?= $this->render('partials/_faq-card', [
                    'faqs' => $faqs,
                    'displayName' => (!empty($college->display_name) ? $college->display_name : $college->name),
                    'pageName' => (!empty($course->short_name) ? $course->short_name : $course->name),
                ]) ?>
            <?php endif; ?>
            <?php if (!empty($courseList)): ?>
                <div class="courseTypeList ciPageList">
                    <div class="courseTypeMaster">
                        <h2 class="allCoursesHeading" style="margin-top: 0px;"><?= !empty($college->display_name) ? $college->display_name : $college->name ?> <?= (!empty($course->short_name) ? $course->short_name : $course->name) ?> Courses List</h2>
                        <?php foreach ($courseList as $list): ?>
                            <div class="courseTypeDiv">
                                <div class="courseNameAndEligibility">
                                    <h3 class="courseName" title="<?= $college->display_name ?? $college->name ?> <?= $list->program ?? $list->course_short_name ?>">
                                        <a href="<?= Url::toCollegeCourseProgram($college->slug, $list->program_slug, $list->pageIndex ?? '') ?>"><?= $list->program ?></a>
                                    </h3>
                                    <span class="courseItems">
                                        <span><?= !empty($list->type) ? CollegeCourse::$type[$list->type] : 'Full Time' ?></span>
                                        <span>• <?= !empty($list->mode) && (!empty(DataHelper::$modeArr[$list->mode])) ? DataHelper::$modeArr[$list->mode] : 'On Campus' ?></span>
                                    </span>
                                </div>
                                <div class="detailedFee">
                                    <?php $dataLocation14 = $isMobile ? UserService::parseDynamicCta('colleges_course_information_wap_lead_slug_card_left_cta8', '', $list->program_slug) : UserService::parseDynamicCta('colleges_course_information_web_lead_{slug}_card_center_cta8', '', $list->program_slug) ?>
                                    <?php $dataLocation15 = $isMobile ? UserService::parseDynamicCta('colleges_course_information_wap_lead_slug_card_right_cta7', '', $list->program_slug) : UserService::parseDynamicCta('colleges_course_information_web_lead_{slug}_card_right_cta7', '', $list->program_slug) ?>
                                    <?php if (!empty($list->fees)): ?>
                                        <p class="courseFee">
                                            <span class="courseFee">₹ <?= ContentHelper::indMoneyFormat($list->fees) ?> </span>
                                            <?php if (!empty($list->duration)): ?>
                                                <span class="courseDuration">(
                                                    <?= CollegeHelper::yearsFormatCourses($list->duration, !empty(CollegeHelper::$programDurationType[$list->duration_type]) ?
                                                        CollegeHelper::$programDurationType[$list->duration_type] : 'Months') ?? '--' ?>)
                                                </span>
                                            <?php endif; ?>
                                        </p>
                                        <div class="lead-cta" id="<?= $list->program_slug ?>" data-slug="<?= $list->program_slug . '-14' ?>" data-lead_cta="14" data-image="<?= $college->logo_image ?? '' ?>" data-entity="college" data-location="<?= $dataLocation14 ?>" data-programId="<?= $list->program_id ?>" data-program="<?= $list->program ?>" data-college_program_id="<?= $list->college_program_id ?>"></div>
                                    <?php else: ?>
                                        <div class="lead-cta" id="<?= $list->program_slug ?>" data-slug="<?= $list->program_slug . '-16' ?>" data-lead_cta="16" data-image="<?= $college->logo_image ?? '' ?>" data-entity="college" data-location="<?= $dataLocation14 ?>" data-programId="<?= $list->program_id ?>"></div>
                                    <?php endif; ?>
                                </div>
                                <div class="applyNowButtonContainer">
                                    <div class="lead-cta" id="<?= $list->program_slug ?>" data-slug="<?= $list->program_slug . '-15' ?>" data-lead_cta="15" data-image="<?= $college->logo_image ?? '' ?>" data-entity="college" data-location="<?= $dataLocation15 ?>" data-programId="<?= $list->program_id ?>"></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <div class="removeFixedQuickLink">
                <!-- Do not Delete this -->
            </div>

            <?php if ($liveApplicationForm && $isMobile && $college->is_sponsored == College::SPONSORED_NO): ?>
                <div id="liveApplicationForm"></div>
            <?php endif; ?>

            <?php if (!empty($faqs) && !$isMobile): ?>
                <?= $this->render('partials/_faq-card', [
                    'faqs' => $faqs,
                    'displayName' => (!empty($college->display_name) ? $college->display_name : $college->name),
                    'pageName' => (!empty($course->short_name) ? $course->short_name : $course->name),
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($otherCourses)):
                unset($otherCourses[$course->name]);
                if (!empty($otherCourses)):
                    ?>
                    <div class="pageData pageInfo ciPageTable" style="margin-bottom: 0px;">
                        <h2>Other Courses Offered by <?= !empty($college->display_name) ? $college->display_name : $college->name ?></h2>
                        <table>
                            <thead>
                                <tr>
                                    <td>Course</td>
                                    <td>Duration</td>
                                    <td>Average Fees</td>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($otherCourses as $other): ?>
                                    <tr>
                                        <?php if (!empty($other['coursePage'])):
                                            ; ?>
                                            <td>
                                                <a href="<?= Url::base() . $other['coursePage'] ?>" title="<?= !empty($college->display_name) ? $college->display_name : $college->name ?>  <?= $other['short_name'] ?>">
                                                    <?= $other['short_name'] ?? $other['name'] ?>
                                                </a>
                                            </td>
                                        <?php else: ?>
                                            <td><a><?= $other['short_name'] ?? $other['name'] ?></a></td>
                                        <?php endif; ?>
                                        <td><?= !empty($other['course_duration']) ? CollegeHelper::yearsFormat($other['course_duration']) : '--' ?></td>
                                        <td><?= !empty($other['avgFees']) ? '₹' . ContentHelper::indMoneyFormat($other['avgFees']) : '--' ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif;
            endif; ?>

            <?php if (!empty($collegeByDiscipline) && !empty($collegeByDiscipline['colleges']) && count($collegeByDiscipline['colleges']) > 2): ?>
                <?= $this->render('partials/_similar-college-card', [
                    'collegeByDiscipline' => $collegeByDiscipline['colleges'],
                    'sponsorStatus' => $collegeByDiscipline['sponsorStatus'],
                    'college' => $college,
                    'cardName' => 'three-cardDisplay',
                ]) ?>
            <?php endif; ?>

            <!-- Nearby colleges -->
            <?php if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
                <section class="pageData">
                    <h2 class="row">
                        Explore Nearby Colleges
                        <?php if (!empty($city->slug) && !$isMobile): ?>
                            <a href="<?= Url::toAllCollege($city->slug) ?>">View All</a>
                        <?php endif; ?>
                    </h2>
                    <div class="customSlider three-cardDisplay">
                        <i class="spriteIcon scrollLeft over"></i>
                        <i class="spriteIcon scrollRight"></i>

                        <div class="customSliderCards">
                            <?php foreach ($nearByCollege as $nbCollege): ?>
                                <div class="sliderCardInfo">

                                    <a href="<?= Url::toCollege($nbCollege->slug) ?>" title="<?= $nbCollege->name ?>">
                                        <figure>
                                            <img class="lazyload" width="275" height="207" loading="lazy" data-src="<?= !empty($nbCollege->cover_image) ? Url::getCollegeBannerImage($nbCollege->cover_image) : Url::toDefaultCollegeBanner() ?>" src="<?= !empty($nbCollege->cover_image) ? Url::getCollegeBannerImage($nbCollege->cover_image) : Url::toDefaultCollegeBanner() ?>" alt="">
                                        </figure>

                                        <div class="textDiv pb-0">
                                            <img class="collegeLogo lazyload" width="56" height="56" loading="lazy" data-src="<?= !empty($nbCollege->logo_image) ? Url::getCollegeLogo($nbCollege->logo_image) : Url::defaultCollegeLogo() ?>" src="<?= !empty($nbCollege->logo_image) ? Url::getCollegeLogo($nbCollege->logo_image) : Url::defaultCollegeLogo() ?>" alt="">
                                            <p class="widgetCardHeading"><?= $nbCollege->name ?></p>
                                        </div>
                                    </a>
                                    <p class="subText pt-0"><span class="spriteIcon locationIcon"></span> <?= !empty($city->name) ? $city->name : '' ?>, <?= !empty($state->name) ? $state->name : '' ?></p>

                                </div>
                            <?php endforeach ?>

                            <?php if (!empty($city->slug) && $isMobile): ?>
                                <div class="sliderCardInfo mobileOnly">
                                    <div class="viewAllDiv">
                                        <a href="<?= Url::toAllCollege($city->slug) ?>"><i class="spriteIcon viewAllIcon"></i>VIEW ALL</a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </section>
            <?php endif; ?>

            <!-- reviews widget -->
            <?php if (!empty($reviews)): ?>
                <?= $this->render('partials/_review-card', [
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Reviews',
                    'reviews' => $reviews,
                    'distrbutionRating' => $revDistributionRating,
                    'categoryRating' => $revCategoryRating,
                    'collegeId' => $college->id,
                    'viewAllURL' => !empty($menus) && !is_numeric($menus['reviews']) ? Url::toCollege($college->slug, 'reviews') : '',
                    'reviewCount' => $reviewCount ?? '',
                    'course' => $course,
                    'category' => 5
                ]) ?>
            <?php endif; ?>
        </div>

        <div class="col-md-4">
            <aside>
                <div class="getSupport">
                    <!-- <div class="row">
                        <img class="lazyload" loading="lazy" width="80" height="80" data-src="/yas/images/bulbIcon.svg" src="/yas/images/bulbIcon.svg" alt="">
                        <p>Are You Interested In This College?</p>
                    </div> -->
                    <div class="lead-cta lead-cta-cls-button" data-lead_cta="0" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>" data-course="<?= !empty($course->id) ? $course->id : null ?>"></div>
                </div>

                <?php if (!empty($college->parent_id)): ?>
                    <?= $this->render('partials/_main-campus', [
                        'college' => $parentCollege,
                    ]) ?>
                <?php endif; ?>

                <?php if (!empty($courseSpecialization)): ?>
                    <div class="sideBarSection">
                        <p class="listCard">Courses You May be Interested</p>
                        <div class="sidebarLinks">
                            <?php foreach ($courseSpecialization as $list): ?>
                                <a href="<?= Url::toCourseDetail($list->slug) ?>" title="<?= $list->name ?>" class="listCard">
                                    <div class="sidebarTextLink">
                                        <p class="cardText"><?= $list->short_name ?></p>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($stateListbyCourse) && !in_array($course->slug, CollegeHelper::$filterGroupMissingCourses)): ?>
                    <div class="sideBarSection">
                        <p class="listCard">Top States for <?= $course->short_name ?></p>
                        <div class="sidebarLinks">
                            <?php foreach ($stateListbyCourse as $list): ?>
                                <a href="/<?= !isset(DataHelper::$collegeFilter301Url[$course->slug]) ? $course->slug : DataHelper::$collegeFilter301Url[$course->slug] ?>-colleges/<?= $list['stateSlug'] ?>" title="<?= $course->short_name ?> colleges in <?= $list['stateName'] ?>" class="listCard">
                                    <div class="sidebarTextLink">
                                        <p class="cardText">Top <?= $course->short_name ?> Colleges In <?= $list['stateName'] ?></p>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                <?php if (!empty($cityListbyCourse) && !in_array($course->slug, CollegeHelper::$filterGroupMissingCourses)): ?>
                    <div class="sideBarSection">
                        <p class="listCard">Top Cities for <?= $course->short_name ?></p>
                        <div class="sidebarLinks">
                            <?php foreach ($cityListbyCourse as $list): ?>
                                <a href="/<?= !isset(DataHelper::$collegeFilter301Url[$course->slug]) ? $course->slug : DataHelper::$collegeFilter301Url[$course->slug] ?>-colleges/<?= $list['citySlug'] ?>" title="<?= $course->short_name ?> colleges in <?= $list['cityName'] ?>" class="listCard">
                                    <div class="sidebarTextLink">
                                        <p class="cardText">Top <?= $course->short_name ?> Colleges In <?= $list['cityName'] ?></p>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($menus)): ?>
                    <div class="sideBarSection">
                        <p class="listCard">Know More About <?= !empty($college->display_name) ? $college->display_name : $college->name ?></p>
                        <div class="sidebarLinks">
                            <?php foreach ($menus as $k => $v):
                                if ($k == 'news') {
                                    continue;
                                }

                                if (in_array($k, CollegeHelper::$removeCollegePageOnCi)) {
                                    continue;
                                }
                                if (!is_string($v)) {
                                    continue;
                                }
                                ?>
                                <a href="<?= Url::toCollege($college->slug, $k) ?>" title="<?= !empty($college->display_name) ? $college->display_name : $college->name ?> <?= $v ?>" class="listCard">
                                    <div class="sidebarTextLink">
                                        <p class="cardText"><?= !empty($college->display_name) ? $college->display_name : $college->name ?> <?= isset(CollegeHelper::$collegePageCi[$k]) ? CollegeHelper::$collegePageCi[$k] : '' ?></p>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php /*if (!empty($featuredNews) || !empty($recentNews)): ?>
                    <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                        'featured' => $featuredNews,
                        'recents' => $recentNews,
                        'isAmp'  => 0,
                        'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                        'smallIcone' => 1
                    ]); ?>
                <?php endif; ?>

                <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                    <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                        'trendings' => $trendingArticles,
                        'recentArticles' => $recentArticles,
                    ]); ?>
                <?php endif; */ ?>
            </aside>
        </div>
    </div>

    <?php if (!empty($forums)): ?>
        <?= $this->render('partials/_forum-card', [
            'forums' => $forums,
            'viewAllUrl' => $college->slug
        ]) ?>
    <?php endif; ?>

    <!-- other colleges under university -->
    <?php if (!empty($affiliatedCollege) && !empty($parentCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Other Colleges under ' . (!empty($parentCollege['display_name']) ? $parentCollege['display_name'] : $parentCollege['name']),
            'colleges' => $affiliatedCollege,
            'city' => $city,
            'state' => $state
        ]) ?>
    <?php endif; ?>

    <!-- Nearby colleges -->
    <?php /*if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Explore Nearby Colleges',
            'colleges' => $nearByCollege,
            'viewAllUrl' => !empty($city) ? "{$city->slug}" : '',
            'city' => $city,
            'state' => $state
        ]) ?>
    <?php endif; */ ?>

    <!-- Popular Colleges -->
    <?php if (!empty($popularCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Popular Colleges',
            'colleges' => $popularCollege,
            'city' => $city,
            'state' => $state
        ])
        ?>
    <?php endif; ?>

    <!-- related article -->
    <?php if (!empty($article)): ?>
        <?= $this->render('../partials/_productArticleCard', [
            'relatedArticles' => $article,
        ]); ?>
    <?php endif; ?>

    <!-- related News -->
    <?php if (!empty($news)): ?>
        <?= $this->render('../partials/_productNewsCard', [
            'news' => $news,
        ]); ?>
    <?php endif;

    /*if ($college->is_google_ads == College::ADS_ACTIVE): ?>
        <aside>
            <div class="horizontalRectangle">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                </div>
            </div>
        </aside>
    <?php endif;*/ ?>
</div>
<?php /* if (!empty($sponsorClientUrl->redirection_link)):?>
     <?= $this->render('partials/_apply-now', [
        'sponsorClientUrl' => $sponsorClientUrl,
        'college' => $college,
        ]) ?>
<?php  endif; */ ?>