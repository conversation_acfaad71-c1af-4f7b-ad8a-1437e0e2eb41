<?php

use yii\db\Migration;

/**
 * <PERSON>les adding columns to table `{{%college}}`.
 */
class m250916_054738_add_is_index_column_to_college_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%college}}', 'is_index', $this->tinyInteger()->defaultValue(1) . ' AFTER is_google_ads');
        $this->addColumn('{{%filter_page_seo}}', 'is_index', $this->tinyInteger()->defaultValue(1) . ' AFTER status');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%college}}', 'is_index');
        $this->dropColumn('{{%filter_page_seo}}', 'is_index');
    }
}
