<?php

namespace common\models;

use common\event\SitemapEvent;
use common\event\CollegeFilterEvent;
use common\event\CollegeEvent;
use common\event\SitemapEventNew;
use common\models\documents\SponsorColleges;
use common\services\ElasticSearchService;
use common\services\MysqlSearchService;
use common\services\SponsorService;
use Exception;
use Yii;
use common\helpers\DataHelper;
use common\services\v2\NewsService;
use yii\behaviors\SluggableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "college".
 *
 * @property int $id
 * @property string|null $name
 * @property string|null $display_name
 * @property string|null $slug
 * @property int $city_id
 * @property string|null $address
 * @property float|null $latitude
 * @property float|null $longitude
 * @property string|null $image
 * @property string|null $cover_image
 * @property string|null $logo_image
 * @property int|null $parent_id
 * @property string|null $country_code
 * @property string|null $url
 * @property string|null $phone
 * @property string|null $email
 * @property int|null $is_popular
 * @property int|null $rank
 * @property int|null $position
 * @property int|null $is_sponsored
 * @property string|null $type
 * @property int|null $status
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property City $city
 * @property Specialization $specialization
 * @property College $parent
 * @property College[] $colleges
 * @property CollegeFeature[] $collegeFeatures
 * @property Feature[] $features
 * @property CollegeProgram[] $collegePrograms
 * @property CollegeCourse[] $collegeCourses
 */
class College extends \yii\db\ActiveRecord
{
    const STATUS_IS_DELETED = 2;
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    const TYPE_INSTITUTES = 2;
    const TYPE_COLLEGE = 1;
    const TYPE_UNIVERSITY = 0;

    const POPULAR_YES = 1;
    const POPULAR_NO = 0;

    const SPONSORED_YES = 1;
    const SPONSORED_NO = 0;

    const IS_FEATURED_YES = 1;
    const IS_FEATURED_NO = 0;

    const ADS_ACTIVE = 0;
    const ADS_INACTIVE = 1;

    const IS_EVENING_YES = 1;
    const IS_EVENING_NO = 0;

    const IS_NO_INDEX_YES = 1;
    const IS_NO_INDEX_NO = 0;

    const IS_GIRL_EXCLUSIVE_YES = 1;
    const IS_GIRL_EXCLUSIVE_NO = 0;

    const ENTITY_COLLEGE = 'college';
    const ENTITY_COLLEGE_LISTING = 'college-listing';
    const ENTITY_COLLEGE_COMPARE = 'college-compare';

    public $exam_id = [];

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            'sluggable' => [
                'class' => SluggableBehavior::class,
                'attribute' => 'name',
                'immutable' => true // chanage it true on production
            ],
            'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'college';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['city_id', 'name'], 'required'],
            [['city_id', 'parent_id', 'rank', 'is_popular', 'position', 'is_sponsored', 'is_featured', 'is_google_ads', 'status'], 'integer'],
            [['college_count', 'is_evening', 'city_id', 'parent_id', 'rank', 'is_popular', 'position', 'is_sponsored', 'is_featured', 'is_google_ads', 'status', 'is_index'], 'integer'],
            [['address'], 'string'],
            [['education_body_type_id', 'college_type', 'location_type', 'is_girl_exclusive', 'primary_stream_id'], 'integer'],
            [['latitude', 'longitude'], 'number'],
            [['created_at', 'updated_at', 'phone', 'email', 'pincode', 'old_id', 'display_name'], 'safe'],
            [['name', 'slug', 'country_code', 'phone', 'email'], 'string', 'max' => 255],
            [['type'], 'string', 'max' => 32],
            [['slug'], 'unique'],
            // [['url'], 'url'],
            [['image', 'cover_image', 'logo_image'], 'file', 'skipOnEmpty' => true, 'skipOnError' => false, 'extensions' => 'webp'],
            [['cover_image'], 'image', 'maxWidth' => '1200', 'maxHeight' => '667', 'maxSize' => 1024 * 100, 'extensions' => 'webp', 'message' => 'Image size should not be greater than 100kb'],
            [['logo_image'], 'image', 'maxWidth' => '276', 'maxHeight' => '207', 'maxSize' => 1024 * 50, 'extensions' => 'webp', 'message' => 'Image size should not be greater than 50kb'],
            // ['cover_image', 'imageValidator'],
            [['education_body_type_id'], 'exist', 'skipOnError' => true, 'targetClass' => EducationBodyType::className(), 'targetAttribute' => ['education_body_type_id' => 'id']],
            [['city_id'], 'exist', 'skipOnError' => true, 'targetClass' => City::className(), 'targetAttribute' => ['city_id' => 'id']],
            [['primary_stream_id'], 'exist', 'skipOnError' => true, 'targetClass' => Stream::className(), 'targetAttribute' => ['primary_stream_id' => 'id']],
            [['parent_id'], 'exist', 'skipOnError' => true, 'targetClass' => College::className(), 'targetAttribute' => ['parent_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'display_name' => 'Display Name',
            'slug' => 'Slug',
            'old_id' => 'Old ID',
            'city_id' => 'City ID',
            'address' => 'Address',
            'latitude' => 'Latitude',
            'longitude' => 'Longitude',
            'image' => 'Image',
            'cover_image' => 'Cover Image',
            'logo_image' => 'Logo Image',
            'parent_id' => 'Parent ID',
            'country_code' => 'Country Code',
            'url' => 'URL',
            'phone' => 'Phone',
            'email' => 'Email',
            'pincode' => 'Pincode',
            'is_popular' => 'Is Popular',
            'rank' => 'Rank',
            'position' => 'Position',
            'is_sponsored' => 'Is Sponsored',
            'type' => 'Type',
            'education_body_type_id' => 'Education Body Type',
            'college_type' => 'College Type',
            'location_type' => 'Location Type',
            'is_google_ads' => 'Is Google Ads',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_index' => 'No Index'
        ];
    }

    /**
     * Gets query for [[City]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CityQuery
     */
    public function getCity()
    {
        return $this->hasOne(City::className(), ['id' => 'city_id']);
    }

    /**
     * Gets query for [[Parent]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeQuery
     */
    public function getParent()
    {
        return $this->hasOne(College::className(), ['id' => 'parent_id']);
    }

    /**
     * Gets query for [[Colleges]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeQuery
     */
    public function getColleges()
    {
        return $this->hasMany(College::className(), ['parent_id' => 'id']);
    }

    /**
     * Gets query for [[CollegeCourses]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeCourseQuery
     */
    public function getCollegeCourses()
    {
        return $this->hasMany(CollegeCourse::className(), ['college_id' => 'id']);
    }

    /**
     * Gets query for [[CollegePrograms]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeProgramQuery
     */
    public function getCollegePrograms()
    {
        return $this->hasMany(CollegeProgram::className(), ['college_id' => 'id']);
    }

    public function getFeatureValues()
    {
        return $this->hasMany(FeatureValue::className(), ['id' => 'feature_value_id'])->viaTable('college_feature_value', ['college_id' => 'id']);
    }

    /**
     * Gets query for [[Exams]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ExamQuery
     */
    public function getExams()
    {
        return $this->hasMany(Exam::className(), ['id' => 'exam_id'])->viaTable('college_exam', ['college_id' => 'id']);
    }

    /**
     * Gets query for [[News]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ExamQuery
     */
    public function getNews()
    {
        $lang_code = DataHelper::getLangId();
        $tableName = NewsService::getInstance('college_news', Yii::$app->controller->id);
        return $this->hasMany(News::className(), ['id' => 'news_id'])->viaTable($tableName, ['college_id' => 'id'])->orderBy(['updated_at' => SORT_DESC])->where(['status' => News::STATUS_ACTIVE])->andWhere(['lang_code' => $lang_code]);
    }

    /**
     * Gets query for [[Article]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ExamQuery
     */
    public function getArticle()
    {
        $lang_code = DataHelper::getLangId();
        return $this->hasMany(Article::className(), ['id' => 'article_id'])->viaTable('article_college', ['college_id' => 'id'])->orderBy(['updated_at' => SORT_DESC])->where(['status' => Article::STATUS_ACTIVE])->andWhere(['lang_code' => $lang_code]);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\CollegeQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\CollegeQuery(get_called_class());
    }

    public function saveExams(array $examIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(College::class . 'id or Exam Ids are required.');
        }

        if (empty($examId) || !$this->isNewRecord) {
            $this->unlinkAll('exams', true);
        }

        foreach ($examIds as $examId) {
            $examModel = Exam::findOne($examId);
            if (!$examModel) {
                continue;
            }

            $this->link('exams', $examModel);
        }

        (new CollegeFilterEvent())->updateCollegeFilter($this);
        (new CollegeFilterEvent())->updateElasticCollegeFilter($this);

        return true;
    }

    public function saveNews(array $newsIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(News::class . ' id is required');
        }

        if (empty($newsIds) || !$this->isNewRecord) {
            $this->unlinkAll('news', true);
        }

        if (!$this->isNewRecord) {
            $this->unlinkAll('news', true);
        }

        foreach ($newsIds as $newsId) {
            $newsModel = News::findOne($newsId);
            if (!$newsModel) {
                continue;
            }

            $this->link('news', $newsModel);
        }
    }

    public function saveArticle(array $articleIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(Article::class . ' id is required');
        }

        if (empty($articleIds) || !$this->isNewRecord) {
            $this->unlinkAll('article', true);
        }

        foreach ($articleIds as $articleId) {
            $articleModel = Article::findOne($articleId);
            if (!$articleModel) {
                continue;
            }

            $this->link('article', $articleModel);
        }
    }

    /**
     * Gets query for [[CollegeContents]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeContentQuery
     */
    public function getCollegeContents()
    {
        return $this->hasMany(CollegeContent::className(), ['entity_id' => 'id']);
    }


    /**
     * Gets query for [[Courses]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeHostelQuery
     */

    public function getHostel()
    {
        return $this->hasMany(CollegeHostel::className(), ['college_id' => 'id']);
    }

    /**
     *
     * Image Height Widhth Validator
     */
    public function imageValidator($attribute, $params)
    {
        if (is_object($this->$attribute)) {
            list($width, $height) = getimagesize($this->photo->tempname);
            if ($width != $params[0] || $height != $params[1]) {
                $this->addError('photo', $attribute . 'size should be ' . $params[0] . ' x ' . $params[1] . ' dimension');
            }
        }
    }

    public function afterSave($insert, $changedAttributes)
    {
        (new SitemapEventNew())->generateCollegeSitemap($this->id);

        $sponserModel = SponsorColleges::find()->where(['college_id' => $this->id])->one();
        if ($sponserModel) {
            $sponserModel->status = $this->is_sponsored;
            $sponserModel->save();
        }

        //update sponsor status based on college status
        $sponsorColleges = SponsorCollege::find()->where(['college_id' => $this->id])->all();

        if (!empty($sponsorColleges) && $this->is_sponsored == College::SPONSORED_NO) {
            foreach ($sponsorColleges as $sponserCollege) {
                $sponserCollege->status = College::STATUS_INACTIVE;
                $sponserCollege->save();
            }
        }

        // (new CollegeFilterEvent())->updateCollegeFilter($this);

        if (YII_ENV == 'prod') {
            // (new CollegeEvent())->updateOldDbCollege($this);
            (new ElasticSearchService)->updateElasticSearch($this, 'college');
        }
        (new CollegeFilterEvent())->updateElasticCollegeFilter($this);
        (new MysqlSearchService)->updateMysqlSearch($this, 'college');

        return parent::afterSave($insert, $changedAttributes);
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }

        return true;
    }

    /**
     * Gets query for [[Courses]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CourseQuery
     */

    public function getCourses()
    {
        return $this->hasMany(Course::className(), ['id' => 'course_id'])->viaTable('college_program', ['college_id' => 'id']);
    }

    public function getCollegeProgram()
    {
        return $this->hasOne(CollegeProgram::className(), ['college_id' => 'id']);
    }

    /* Gets query for [[CollegeCourses]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\BusinessEntityQuery
     */
    public function getBusinessEntity()
    {
        return $this->hasMany(BusinessEntity::className(), ['entity_id' => 'id']);
    }

    public function getCollgeStreamRank()
    {
        return $this->hasMany(CollegeStreamRank::className(), ['college_id' => 'id']);
    }
}
