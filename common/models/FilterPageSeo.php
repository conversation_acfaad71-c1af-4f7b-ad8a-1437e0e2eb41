<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "filter_page_seo".
 *
 * @property int $id
 * @property string $slug
 * @property string|null $h1
 * @property string|null $meta_title
 * @property string|null $meta_description
 * @property string|null $content
 * @property int|null $status
 * @property int|null $is_index
 * @property string|null $created_at
 * @property string|null $updated_at
 */
class FilterPageSeo extends \yii\db\ActiveRecord
{
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;
    const ENTITY_FILTER_PAGE_SEO = 'filter-seo';

    const IS_INDEX_YES = 1;
    const IS_INDEX_NO = 0;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'filter_page_seo';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['status', 'localize_year', 'is_index'], 'integer'],
            [['slug'], 'required'],
            [['slug'], 'unique'],
            [['content', 'bottom_content'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['slug', 'h1', 'meta_title', 'meta_description'], 'string', 'max' => 255],
            [['slug'], 'match', 'pattern' => '/^[a-z\/-]+$/', 'message' => 'Slug Contains only small alphabets and "-" & "/" symbol'],

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'slug' => 'Slug',
            'localize_year' => 'Localize Year',
            'h1' => 'H1',
            'meta_title' => 'Meta Title',
            'meta_description' => 'Meta Description',
            'content' => 'Top Content',
            'bottom_content' => 'Bottom Content',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_index' => 'No Index'
        ];
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\FilterPageSeoQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\FilterPageSeoQuery(get_called_class());
    }
}
